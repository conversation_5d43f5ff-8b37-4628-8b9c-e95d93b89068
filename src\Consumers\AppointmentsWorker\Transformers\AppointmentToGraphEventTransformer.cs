using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Graph.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Transformers
{
    /// <summary>
    /// Transforms appointment messages to Microsoft Graph Event objects
    /// </summary>
    public static class AppointmentToGraphEventTransformer
    {
        /// <summary>
        /// Transforms an AppointmentMessage to a Microsoft Graph Event
        /// </summary>
        /// <param name="appointmentMessage">The appointment message to transform</param>
        /// <returns>A Microsoft Graph Event object</returns>
        public static Event TransformToGraphEvent(AppointmentMessage appointmentMessage)
        {
            if (appointmentMessage?.Data == null)
                throw new ArgumentNullException(nameof(appointmentMessage), "Appointment message and data cannot be null");

            var graphEvent = new Event
            {
                Subject = appointmentMessage.Data.Appo_Subject ?? "No Subject",
                Body = new ItemBody
                {
                    ContentType = BodyType.Html,
                    Content = appointmentMessage.Data.Appo_Description ?? string.Empty
                },
                Start = new DateTimeTimeZone
                {
                    DateTime = appointmentMessage.StartTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK"),
                    TimeZone = "UTC"
                },
                End = new DateTimeTimeZone
                {
                    DateTime = appointmentMessage.EndTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK"),
                    TimeZone = "UTC"
                },
                Location = new Location
                {
                    DisplayName = appointmentMessage.Data.Appo_Location ?? string.Empty
                },
                IsAllDay = appointmentMessage.IsAllDay,
                Importance = ParseImportance(appointmentMessage.Importance),
                Sensitivity = ParseSensitivity(appointmentMessage.Sensitivity, appointmentMessage.Data.Appo_IsPrivate),
                ShowAs = FreeBusyStatus.Busy,
                IsReminderOn = true,
                ReminderMinutesBeforeStart = 15,
                Categories = new List<string>(),
                Attendees = TransformAttendees(appointmentMessage.Attendees)
            };

            // Add custom properties for tracking
            if (graphEvent.SingleValueExtendedProperties == null)
                graphEvent.SingleValueExtendedProperties = new List<SingleValueLegacyExtendedProperty>();

            // Add appointment ID as extended property for tracking
            if (appointmentMessage.Data.Appo_Id.HasValue)
            {
                graphEvent.SingleValueExtendedProperties.Add(new SingleValueLegacyExtendedProperty
                {
                    Id = "String {66f5a359-4659-4830-9070-00047ec6ac6e} Name AppointmentId",
                    Value = appointmentMessage.Data.Appo_Id.Value.ToString()
                });
            }

            // Add firm ID as extended property
            if (appointmentMessage.Data.FirmId.HasValue)
            {
                graphEvent.SingleValueExtendedProperties.Add(new SingleValueLegacyExtendedProperty
                {
                    Id = "String {66f5a359-4659-4830-9070-00047ec6ac6e} Name FirmId",
                    Value = appointmentMessage.Data.FirmId.Value.ToString()
                });
            }

            return graphEvent;
        }

        /// <summary>
        /// Creates a CalendarEventWrapper from an AppointmentMessage
        /// </summary>
        /// <param name="appointmentMessage">The appointment message to transform</param>
        /// <returns>A CalendarEventWrapper containing the Graph Event</returns>
        public static CalendarEventWrapper TransformToCalendarEventWrapper(AppointmentMessage appointmentMessage)
        {
            if (appointmentMessage?.Data == null)
                throw new ArgumentNullException(nameof(appointmentMessage), "Appointment message and data cannot be null");

            var graphEvent = TransformToGraphEvent(appointmentMessage);

            return new CalendarEventWrapper
            {
                TenantId = appointmentMessage.Data.FirmId ?? 0,
                UserId = appointmentMessage.Data.UserId ?? 0,
                Operation = appointmentMessage.ToOperationType(),
                CorrelationId = appointmentMessage.CorrelationId,
                GraphEvent = graphEvent,
                SyncStatus = SyncStatus.Pending,
                CreatedAt = DateTimeOffset.UtcNow
            };
        }

        /// <summary>
        /// Updates an existing Graph Event with data from an AppointmentMessage
        /// </summary>
        /// <param name="existingEvent">The existing Graph Event to update</param>
        /// <param name="appointmentMessage">The appointment message with updated data</param>
        /// <returns>The updated Graph Event</returns>
        public static Event UpdateGraphEvent(Event existingEvent, AppointmentMessage appointmentMessage)
        {
            if (existingEvent == null)
                throw new ArgumentNullException(nameof(existingEvent));
            
            if (appointmentMessage?.Data == null)
                throw new ArgumentNullException(nameof(appointmentMessage), "Appointment message and data cannot be null");

            // Update the event properties
            existingEvent.Subject = appointmentMessage.Data.Appo_Subject ?? existingEvent.Subject;
            
            if (existingEvent.Body == null)
                existingEvent.Body = new ItemBody();
            
            existingEvent.Body.Content = appointmentMessage.Data.Appo_Description ?? existingEvent.Body.Content;
            existingEvent.Body.ContentType = BodyType.Html;

            if (existingEvent.Start == null)
                existingEvent.Start = new DateTimeTimeZone();
            
            existingEvent.Start.DateTime = appointmentMessage.StartTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
            existingEvent.Start.TimeZone = "UTC";

            if (existingEvent.End == null)
                existingEvent.End = new DateTimeTimeZone();
            
            existingEvent.End.DateTime = appointmentMessage.EndTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
            existingEvent.End.TimeZone = "UTC";

            if (existingEvent.Location == null)
                existingEvent.Location = new Location();
            
            existingEvent.Location.DisplayName = appointmentMessage.Data.Appo_Location ?? existingEvent.Location.DisplayName;

            existingEvent.IsAllDay = appointmentMessage.IsAllDay;
            existingEvent.Importance = ParseImportance(appointmentMessage.Importance);
            existingEvent.Sensitivity = ParseSensitivity(appointmentMessage.Sensitivity, appointmentMessage.Data.Appo_IsPrivate);

            // Update attendees if provided
            if (appointmentMessage.Attendees != null && appointmentMessage.Attendees.Any())
            {
                existingEvent.Attendees = TransformAttendees(appointmentMessage.Attendees);
            }

            return existingEvent;
        }

        /// <summary>
        /// Transforms attendee strings to Graph Attendee objects
        /// </summary>
        private static IList<Attendee>? TransformAttendees(IList<string>? attendeeEmails)
        {
            if (attendeeEmails == null || !attendeeEmails.Any())
                return null;

            return attendeeEmails.Select(email => new Attendee
            {
                EmailAddress = new EmailAddress
                {
                    Address = email,
                    Name = email // Use email as name if no display name is available
                },
                Type = AttendeeType.Required
            }).ToList();
        }

        /// <summary>
        /// Parses importance string to Graph Importance enum
        /// </summary>
        private static Importance ParseImportance(string? importance)
        {
            if (string.IsNullOrEmpty(importance))
                return Importance.Normal;

            return importance.ToLowerInvariant() switch
            {
                "high" => Importance.High,
                "low" => Importance.Low,
                _ => Importance.Normal
            };
        }

        /// <summary>
        /// Parses sensitivity string and privacy flag to Graph Sensitivity enum
        /// </summary>
        private static Sensitivity ParseSensitivity(string? sensitivity, bool? isPrivate)
        {
            if (isPrivate == true)
                return Sensitivity.Private;

            if (string.IsNullOrEmpty(sensitivity))
                return Sensitivity.Normal;

            return sensitivity.ToLowerInvariant() switch
            {
                "private" => Sensitivity.Private,
                "personal" => Sensitivity.Personal,
                "confidential" => Sensitivity.Confidential,
                _ => Sensitivity.Normal
            };
        }

        /// <summary>
        /// Extracts the appointment ID from a Graph Event's extended properties
        /// </summary>
        /// <param name="graphEvent">The Graph Event to extract from</param>
        /// <returns>The appointment ID if found, null otherwise</returns>
        public static int? ExtractAppointmentId(Event graphEvent)
        {
            if (graphEvent?.SingleValueExtendedProperties == null)
                return null;

            var appointmentIdProperty = graphEvent.SingleValueExtendedProperties
                .FirstOrDefault(p => p.Id?.Contains("AppointmentId") == true);

            if (appointmentIdProperty?.Value != null && int.TryParse(appointmentIdProperty.Value, out var appointmentId))
                return appointmentId;

            return null;
        }

        /// <summary>
        /// Extracts the firm ID from a Graph Event's extended properties
        /// </summary>
        /// <param name="graphEvent">The Graph Event to extract from</param>
        /// <returns>The firm ID if found, null otherwise</returns>
        public static int? ExtractFirmId(Event graphEvent)
        {
            if (graphEvent?.SingleValueExtendedProperties == null)
                return null;

            var firmIdProperty = graphEvent.SingleValueExtendedProperties
                .FirstOrDefault(p => p.Id?.Contains("FirmId") == true);

            if (firmIdProperty?.Value != null && int.TryParse(firmIdProperty.Value, out var firmId))
                return firmId;

            return null;
        }
    }
}
