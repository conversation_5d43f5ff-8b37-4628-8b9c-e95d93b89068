using System;
using Microsoft.Graph.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models
{
    /// <summary>
    /// Extended calendar event that wraps Microsoft Graph Event with additional sync metadata
    /// </summary>
    public class CalendarEventWrapper
    {
        /// <summary>
        /// The Microsoft Graph Event object
        /// </summary>
        public Event GraphEvent { get; set; } = new Event();

        /// <summary>
        /// Correlation ID for tracking the event through the system
        /// </summary>
        public string CorrelationId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Tenant identifier (FirmId)
        /// </summary>
        public int TenantId { get; set; }

        /// <summary>
        /// User identifier who owns this calendar event
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Operation type for this event
        /// </summary>
        public OperationType Operation { get; set; }

        /// <summary>
        /// Current synchronization status
        /// </summary>
        public SyncStatus SyncStatus { get; set; } = SyncStatus.Pending;

        /// <summary>
        /// Last time this event was synchronized
        /// </summary>
        public DateTimeOffset? LastSyncedAt { get; set; }

        /// <summary>
        /// Number of synchronization attempts
        /// </summary>
        public int SyncAttempts { get; set; } = 0;

        /// <summary>
        /// Error details if synchronization failed
        /// </summary>
        public string? SyncErrorDetails { get; set; }

        /// <summary>
        /// Event creation timestamp
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>
        /// Event last modified timestamp
        /// </summary>
        public DateTimeOffset? ModifiedAt { get; set; }

        /// <summary>
        /// Gets or sets the event ID from the Graph Event
        /// </summary>
        public string? EventId
        {
            get => GraphEvent?.Id;
            set => GraphEvent.Id = value;
        }

        /// <summary>
        /// Gets or sets the event subject from the Graph Event
        /// </summary>
        public string? Subject
        {
            get => GraphEvent?.Subject;
            set => GraphEvent.Subject = value;
        }

        /// <summary>
        /// Creates a CalendarEventWrapper from an AppointmentMessage using the transformer
        /// </summary>
        public static CalendarEventWrapper FromAppointmentMessage(AppointmentMessage message)
        {
            return Transformers.AppointmentToGraphEventTransformer.TransformToCalendarEventWrapper(message);
        }
    }

    /// <summary>
    /// Synchronization status for calendar events
    /// </summary>
    public enum SyncStatus
    {
        /// <summary>
        /// Event is pending synchronization
        /// </summary>
        Pending,

        /// <summary>
        /// Event has been successfully synchronized
        /// </summary>
        Synced,

        /// <summary>
        /// Event synchronization failed
        /// </summary>
        Failed,

        /// <summary>
        /// Event synchronization is in progress
        /// </summary>
        InProgress,

        /// <summary>
        /// Event synchronization was skipped
        /// </summary>
        Skipped
    }
}