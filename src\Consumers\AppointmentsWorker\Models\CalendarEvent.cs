using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models
{
    /// <summary>
    /// Represents a calendar event for synchronization with external calendar systems
    /// </summary>
    public class CalendarEvent
    {
        /// <summary>
        /// Unique identifier for the calendar event
        /// </summary>
        public string EventId { get; set; } = string.Empty;

        /// <summary>
        /// Correlation ID for tracking the event through the system
        /// </summary>
        public string CorrelationId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Tenant identifier
        /// </summary>
        public int TenantId { get; set; }

        /// <summary>
        /// User identifier who owns this calendar event
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Event title/subject
        /// </summary>
        [Required]
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// Event description/body
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Event start time
        /// </summary>
        public DateTimeOffset StartTime { get; set; }

        /// <summary>
        /// Event end time
        /// </summary>
        public DateTimeOffset EndTime { get; set; }

        /// <summary>
        /// Event location
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// Event organizer
        /// </summary>
        public string? Organizer { get; set; }

        /// <summary>
        /// List of event attendees
        /// </summary>
        public List<MessageAttendee>? Attendees { get; set; }

        /// <summary>
        /// Recurrence rule for recurring events
        /// </summary>
        public string? RecurrenceRule { get; set; }

        /// <summary>
        /// Flag indicating if this is an all-day event
        /// </summary>
        public bool IsAllDay { get; set; }

        /// <summary>
        /// Event status (e.g., confirmed, tentative, cancelled)
        /// </summary>
        public string? Status { get; set; }

        /// <summary>
        /// External identifier in the calendar system
        /// </summary>
        public string? ExternalId { get; set; }

        /// <summary>
        /// External system name (e.g., "Outlook", "Google")
        /// </summary>
        public string? ExternalSystem { get; set; }

        /// <summary>
        /// Operation type for this event
        /// </summary>
        public OperationType Operation { get; set; }

        /// <summary>
        /// Current synchronization status
        /// </summary>
        public SyncStatus SyncStatus { get; set; } = SyncStatus.Pending;

        /// <summary>
        /// Last time this event was synchronized
        /// </summary>
        public DateTimeOffset? LastSyncedAt { get; set; }

        /// <summary>
        /// Number of synchronization attempts
        /// </summary>
        public int SyncAttempts { get; set; } = 0;

        /// <summary>
        /// Error details if synchronization failed
        /// </summary>
        public string? SyncErrorDetails { get; set; }

        /// <summary>
        /// Event creation timestamp
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>
        /// Event last modified timestamp
        /// </summary>
        public DateTimeOffset? ModifiedAt { get; set; }

        /// <summary>
        /// Event importance level
        /// </summary>
        public string? Importance { get; set; }

        /// <summary>
        /// Event sensitivity level
        /// </summary>
        public string? Sensitivity { get; set; }

        /// <summary>
        /// Converts the calendar event to a Graph API event payload
        /// </summary>
        public object ToGraphEventPayload()
        {
            var payload = new
            {
                subject = Title,
                body = new
                {
                    contentType = "HTML",
                    content = Description ?? string.Empty
                },
                start = new
                {
                    dateTime = StartTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK"),
                    timeZone = "UTC"
                },
                end = new
                {
                    dateTime = EndTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK"),
                    timeZone = "UTC"
                },
                location = new
                {
                    displayName = Location ?? string.Empty
                },
                isAllDay = IsAllDay,
                importance = Importance ?? "normal",
                sensitivity = Sensitivity ?? "normal",
                attendees = Attendees?.Select(a => new
                {
                    emailAddress = new
                    {
                        address = a.Email,
                        name = a.Name ?? a.Email
                    },
                    type = a.Type ?? "required"
                }).ToArray() ?? Array.Empty<object>()
            };

            return payload;
        }
    }

    /// <summary>
    /// Synchronization status for calendar events
    /// </summary>
    public enum SyncStatus
    {
        /// <summary>
        /// Event is pending synchronization
        /// </summary>
        Pending,

        /// <summary>
        /// Event has been successfully synchronized
        /// </summary>
        Synced,

        /// <summary>
        /// Event synchronization failed
        /// </summary>
        Failed,

        /// <summary>
        /// Event synchronization is in progress
        /// </summary>
        InProgress,

        /// <summary>
        /// Event synchronization was skipped
        /// </summary>
        Skipped
    }

    /// <summary>
    /// Represents an attendee for a calendar event
    /// </summary>
    public class MessageAttendee
    {
        /// <summary>
        /// Attendee email address
        /// </summary>
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Attendee display name
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// Attendee type (required, optional, resource)
        /// </summary>
        public string? Type { get; set; } = "required";

        /// <summary>
        /// Attendee response status
        /// </summary>
        public string? ResponseStatus { get; set; }
    }
}