using System;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models
{
    /// <summary>
    /// Represents a user's calendar synchronization configuration including access tokens
    /// </summary>
    public class UserSyncConfig
    {
        /// <summary>
        /// Unique identifier for the sync configuration
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// User identifier
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Tenant identifier
        /// </summary>
        public int TenantId { get; set; }

        /// <summary>
        /// User email address
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Security token information (JSON)
        /// </summary>
        public string? Security { get; set; }

        /// <summary>
        /// Refresh token for OAuth flow
        /// </summary>
        public string? RefreshToken { get; set; }

        /// <summary>
        /// Token expiration timestamp
        /// </summary>
        public DateTime TokenExpiry { get; set; }

        /// <summary>
        /// Flag indicating if the token is valid
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// Flag indicating if synchronization is enabled for this user
        /// </summary>
        public bool SyncEnabled { get; set; } = true;

        /// <summary>
        /// Last time synchronization was performed
        /// </summary>
        public DateTime? LastSyncTime { get; set; }

        /// <summary>
        /// Maximum number of sync retries before giving up
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Current retry count for token refresh
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// When the token was last refreshed
        /// </summary>
        public DateTime? LastRefreshAttempt { get; set; }

        /// <summary>
        /// Token creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// User-specific settings for calendar synchronization (JSON)
        /// </summary>
        public string? SyncSettings { get; set; }

        /// <summary>
        /// Determines if the token needs refresh based on expiry time
        /// </summary>
        public bool NeedsRefresh => DateTime.UtcNow.AddMinutes(5) > TokenExpiry;

        /// <summary>
        /// Determines if token refresh should be attempted based on retry count
        /// </summary>
        public bool CanRetryRefresh => RetryCount < MaxRetries;

        /// <summary>
        /// When the refresh token was last updated
        /// </summary>
        public DateTimeOffset? RefreshTokenUpdatedAt { get; set; }

        /// <summary>
        /// Flag indicating if the refresh token is valid
        /// </summary>
        public bool IsRefreshTokenValid { get; set; } = true;
    }

    /// <summary>
    /// Tracks the processing status of an appointment
    /// </summary>
    public class AppointmentProcessingStatus
    {
        /// <summary>
        /// Unique identifier
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Appointment identifier
        /// </summary>
        public int AppointmentId { get; set; }

        /// <summary>
        /// Tenant identifier
        /// </summary>
        public int TenantId { get; set; }

        /// <summary>
        /// User identifier
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Flag indicating if processing is complete
        /// </summary>
        public bool IsProcessed { get; set; }

        /// <summary>
        /// Processing status (Pending, Processed, Failed)
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// Error message if processing failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// External identifier in the calendar system
        /// </summary>
        public string? GraphResponseId { get; set; }

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Processing completion timestamp
        /// </summary>
        public DateTime? ProcessedAt { get; set; }

        /// <summary>
        /// Number of processing attempts
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// Flag indicating if this item was sent to dead letter queue
        /// </summary>
        public bool IsDead { get; set; } = false;

        /// <summary>
        /// When this item was moved to dead letter queue
        /// </summary>
        public DateTime? DeadAt { get; set; }

        /// <summary>
        /// Reason for dead letter status
        /// </summary>
        public string? DeadReason { get; set; }

        /// <summary>
        /// Flag indicating if this is a replay of a previously failed message
        /// </summary>
        public bool IsReplay { get; set; } = false;

        /// <summary>
        /// Timestamp of the original message if this is a replay
        /// </summary>
        public DateTime? OriginalTimestamp { get; set; }
    }
}
