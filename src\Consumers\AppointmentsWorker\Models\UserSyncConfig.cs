using System;
using Newtonsoft.Json;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models
{
    /// <summary>
    /// Represents a user's calendar synchronization configuration from T_UserSyncConfig table
    /// </summary>
    public class UserSyncConfig
    {
        /// <summary>
        /// Unique identifier for the sync configuration
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// User identifier
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Firm identifier (tenant)
        /// </summary>
        public int FirmId { get; set; }

        /// <summary>
        /// Provider name (e.g., "Microsoft", "Google")
        /// </summary>
        public string? Provider { get; set; }

        /// <summary>
        /// Security token information (JSON containing accessToken and refreshToken)
        /// </summary>
        public string? Security { get; set; }

        /// <summary>
        /// Account email address
        /// </summary>
        public string? AccountEmail { get; set; }

        /// <summary>
        /// Flag indicating if immutable IDs are inserted
        /// </summary>
        public bool IsImmutableIdsInserted { get; set; }

        /// <summary>
        /// Flag indicating if initial sync email was sent
        /// </summary>
        public bool IsInitialSyncEmailSent { get; set; }

        /// <summary>
        /// Activity status identifier
        /// </summary>
        public int ActivityStatusId { get; set; }

        /// <summary>
        /// Parsed security tokens from the Security JSON field
        /// </summary>
        [JsonIgnore]
        public SecurityTokens? SecurityTokens
        {
            get
            {
                if (string.IsNullOrEmpty(Security))
                    return null;

                try
                {
                    return JsonConvert.DeserializeObject<SecurityTokens>(Security);
                }
                catch
                {
                    return null;
                }
            }
            set
            {
                Security = value != null ? JsonConvert.SerializeObject(value) : null;
            }
        }

        /// <summary>
        /// Gets the access token from the Security JSON
        /// </summary>
        [JsonIgnore]
        public string? AccessToken => SecurityTokens?.AccessToken;

        /// <summary>
        /// Gets the refresh token from the Security JSON
        /// </summary>
        [JsonIgnore]
        public string? RefreshToken => SecurityTokens?.RefreshToken;

        /// <summary>
        /// Determines if the tokens are valid (not "Invalid")
        /// </summary>
        [JsonIgnore]
        public bool HasValidTokens =>
            SecurityTokens != null &&
            !string.IsNullOrEmpty(SecurityTokens.AccessToken) &&
            SecurityTokens.AccessToken != "Invalid" &&
            !string.IsNullOrEmpty(SecurityTokens.RefreshToken) &&
            SecurityTokens.RefreshToken != "Invalid";
    }

    /// <summary>
    /// Represents the security tokens stored in the Security JSON field
    /// </summary>
    public class SecurityTokens
    {
        [JsonProperty("accessToken")]
        public string? AccessToken { get; set; }

        [JsonProperty("refreshToken")]
        public string? RefreshToken { get; set; }
    }

    /// <summary>
    /// Tracks the processing status of an appointment
    /// </summary>
    public class AppointmentProcessingStatus
    {
        /// <summary>
        /// Unique identifier
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Appointment identifier
        /// </summary>
        public int AppointmentId { get; set; }

        /// <summary>
        /// Tenant identifier
        /// </summary>
        public int TenantId { get; set; }

        /// <summary>
        /// User identifier
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Flag indicating if processing is complete
        /// </summary>
        public bool IsProcessed { get; set; }

        /// <summary>
        /// Processing status (Pending, Processed, Failed)
        /// </summary>
        public string Status { get; set; } = "Pending";

        /// <summary>
        /// Error message if processing failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// External identifier in the calendar system
        /// </summary>
        public string? GraphResponseId { get; set; }

        /// <summary>
        /// Creation timestamp
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Processing completion timestamp
        /// </summary>
        public DateTime? ProcessedAt { get; set; }

        /// <summary>
        /// Number of processing attempts
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// Flag indicating if this item was sent to dead letter queue
        /// </summary>
        public bool IsDead { get; set; } = false;

        /// <summary>
        /// When this item was moved to dead letter queue
        /// </summary>
        public DateTime? DeadAt { get; set; }

        /// <summary>
        /// Reason for dead letter status
        /// </summary>
        public string? DeadReason { get; set; }

        /// <summary>
        /// Flag indicating if this is a replay of a previously failed message
        /// </summary>
        public bool IsReplay { get; set; } = false;

        /// <summary>
        /// Timestamp of the original message if this is a replay
        /// </summary>
        public DateTime? OriginalTimestamp { get; set; }
    }
}
