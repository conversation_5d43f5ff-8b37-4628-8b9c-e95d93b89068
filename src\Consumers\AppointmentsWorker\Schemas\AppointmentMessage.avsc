{"type": "record", "name": "AppointmentMessage", "namespace": "Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models", "fields": [{"name": "Version", "type": ["null", "long"], "default": null}, {"name": "Operation", "type": ["null", "string"], "default": null}, {"name": "CorrelationId", "type": "string", "default": ""}, {"name": "CalendarEventId", "type": ["null", "string"], "default": null}, {"name": "MessageId", "type": "string", "default": ""}, {"name": "Timestamp", "type": "long", "default": 0}, {"name": "Body", "type": ["null", "string"], "default": null}, {"name": "BodyType", "type": ["null", "string"], "default": null}, {"name": "Attendees", "type": ["null", {"type": "array", "items": {"type": "record", "name": "MessageAttendee", "fields": [{"name": "Email", "type": ["null", "string"], "default": null}, {"name": "Name", "type": ["null", "string"], "default": null}, {"name": "Type", "type": ["null", "string"], "default": null}]}}], "default": null}, {"name": "IsAllDay", "type": "boolean", "default": false}, {"name": "Importance", "type": ["null", "string"], "default": null}, {"name": "Sensitivity", "type": ["null", "string"], "default": null}, {"name": "Id", "type": ["null", "string"], "default": null}, {"name": "Columns", "type": ["null", "int"], "default": null}, {"name": "FirmId", "type": ["null", "int"], "default": null}, {"name": "ExternalEventId", "type": ["null", "string"], "default": null}, {"name": "RecurrencePattern", "type": ["null", "string"], "default": null}, {"name": "ModifiedDate", "type": ["null", "long"], "default": null}, {"name": "ModifiedBy", "type": ["null", "string"], "default": null}, {"name": "Status", "type": ["null", "string"], "default": null}, {"name": "Data", "type": ["null", {"type": "record", "name": "AppointmentData", "fields": [{"name": "FirmId", "type": ["null", "int"], "default": null}, {"name": "UserId", "type": ["null", "int"], "default": null}, {"name": "Appo_Subject", "type": ["null", "string"], "default": null}, {"name": "MatterId", "type": ["null", "int"], "default": null}, {"name": "Appo_Description", "type": ["null", "string"], "default": null}, {"name": "Appo_Location", "type": ["null", "string"], "default": null}, {"name": "Appo_ActiveStatusId", "type": ["null", "int"], "default": null}, {"name": "Appo_Id", "type": ["null", "int"], "default": null}, {"name": "Appo_IsPrivate", "type": ["null", "boolean"], "default": null}, {"name": "Appo_StartTime", "type": ["null", "long"], "default": null}, {"name": "Appo_EndTime", "type": ["null", "long"], "default": null}]}], "default": null}, {"name": "Database", "type": ["null", "string"], "default": null}]}