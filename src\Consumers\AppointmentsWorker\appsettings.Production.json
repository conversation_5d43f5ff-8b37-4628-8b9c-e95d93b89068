{"Application": {"EnableDetailedLogs": false, "RetryMaxAttempts": 8, "RetryDelayInSeconds": 3, "CircuitBreakerThreshold": 8, "CircuitBreakerDurationInSeconds": 120, "TimeoutInSeconds": 60, "DeadLetterQueueEnabled": true}, "Kafka": {"BootstrapServers": "kafka-f68bd74-caretlegal-data-bus.b.aivencloud.com:15904", "UseSecureConnection": true, "SaslUsername": "a<PERSON><PERSON><PERSON><PERSON>", "SaslPassword": "AVNS_Boi1zX2ngjmFsqmsd1s", "MessageTimeoutMs": 600000, "BatchSize": 200}, "Database": {"SystemConnectionString": "Data Source=zolaproddb.amicusprod.amicuscreative.com;Initial Catalog=zola-prod;Trusted_Connection=True;max pool size=1000;TrustServerCertificate=True", "MaxRetries": 8, "RetryDelaySeconds": 3, "CommandTimeoutSeconds": 60, "EnableSensitiveDataLogging": false, "ConnectionPooling": {"MinPoolSize": 10, "MaxPoolSize": 1000, "ConnectionLifetimeSeconds": 600}}, "MicrosoftGraphApi": {"Login": "https://login.microsoftonline.com", "GraphUrlBase": "https://graph.microsoft.com/v1.0", "ClientId": "151b6300-7b83-4530-9838-7ca8f25d2efd", "ClientSecret": "**********************************", "GraphScopes": ["https://graph.microsoft.com/.default"], "GraphRedirectUrl": "https://app.zola.com/Settings/UserSettings/OutlookFolderMapping.aspx", "RedirectUrl": "https://app.zola.com/calendar/sync/outlookredirecturlcalendarsync.aspx", "TenantId": "common"}, "Resilience": {"GraphApi": {"RetryCount": 5, "RetryDelayInSeconds": 3, "CircuitBreakerThreshold": 10, "CircuitBreakerDurationInSeconds": 120}, "Database": {"RetryCount": 8, "RetryDelayInSeconds": 2, "CircuitBreakerThreshold": 15, "CircuitBreakerDurationInSeconds": 180}, "TokenAcquisition": {"RetryCount": 5, "RetryDelayInSeconds": 2, "CircuitBreakerThreshold": 10, "CircuitBreakerDurationInSeconds": 600}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "Datadog": {"Enabled": true, "Environment": "Production", "Service": "AppointmentsConsumer", "Tags": ["env:production", "project:caretlegal", "component:appointments"]}}