using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Serialization;
using Microsoft.Extensions.Logging;
using Polly;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Service for interacting with Microsoft Graph API with support for batch operations,
    /// resilience patterns and comprehensive error handling.
    /// </summary>
    public class GraphApiService
    {
        private readonly ILogger<GraphApiService> _logger;
        private readonly TokenManagementService _tokenManagementService;
        private readonly HttpClient _httpClient;
        private readonly IAsyncPolicy _graphApiPolicy;

        // Constants
        private const string GraphApiEndpoint = "https://graph.microsoft.com/v1.0";
        private const int BatchRequestLimit = 20; // Microsoft Graph API batch request limit
        private const string JsonMediaType = "application/json";

        public GraphApiService(
            ILogger<GraphApiService> logger,
            TokenManagementService tokenManagementService,
            IHttpClientFactory httpClientFactory)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenManagementService = tokenManagementService ?? throw new ArgumentNullException(nameof(tokenManagementService));
            _httpClient = httpClientFactory.CreateClient("GraphApi");
            _httpClient.BaseAddress = new Uri(GraphApiEndpoint);
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(JsonMediaType));

            // Set up resilience policy for Graph API operations
            _graphApiPolicy = ResiliencePolicies.CreateGraphApiPolicy(logger);
        }

        /// <summary>
        /// Creates a calendar event in the user's default calendar
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created event with server-side properties populated</returns>
        public async Task<CalendarEvent> CreateEventAsync(
            UserSyncConfig userConfig,
            CalendarEvent calendarEvent,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvent == null) throw new ArgumentNullException(nameof(calendarEvent));

            string accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var request = new HttpRequestMessage(HttpMethod.Post, $"/users/{userConfig.UserId}/calendar/events");
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                    request.Content = new StringContent(JsonHelper.Serialize(calendarEvent.ToGraphEventPayload()),
                        Encoding.UTF8, new MediaTypeHeaderValue(JsonMediaType));

                    var response = await _httpClient.SendAsync(request, cancellationToken);

                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync(cancellationToken);
                        var createdEvent = JsonHelper.Deserialize<Dictionary<string, object>>(content);

                        // Extract the event ID and update our local event
                        var eventId = createdEvent["id"].ToString();
                        calendarEvent.EventId = eventId;
                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;

                        _logger.LogInformation("Successfully created event {EventId} for user {UserId}",
                            eventId, userConfig.UserId);

                        return calendarEvent;
                    }
                    else
                    {
                        await HandleErrorResponseAsync(response, userConfig, cancellationToken);
                        // If HandleErrorResponseAsync didn't throw, we should still throw an exception
                        throw new GraphApiException($"Failed to create event for user {userConfig.UserId}",
                            (int)response.StatusCode);
                    }
                }
                catch (HttpRequestException ex)
                {
                    _logger.LogError(ex, "HTTP error occurred when creating event for user {UserId}", userConfig.UserId);
                    throw new GraphApiException($"HTTP error creating event for user {userConfig.UserId}", null, ex);
                }
                catch (Exception ex) when (ex is not GraphApiException)
                {
                    _logger.LogError(ex, "Unexpected error creating event for user {UserId}", userConfig.UserId);
                    throw new GraphApiException($"Unexpected error creating event for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Updates an existing calendar event
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated event</returns>
        public async Task<CalendarEvent> UpdateEventAsync(
            UserSyncConfig userConfig,
            CalendarEvent calendarEvent,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvent == null) throw new ArgumentNullException(nameof(calendarEvent));
            if (string.IsNullOrEmpty(calendarEvent.EventId))
                throw new ArgumentException("Calendar event must have an EventId for updates", nameof(calendarEvent));

            string accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var request = new HttpRequestMessage(HttpMethod.Patch,
                        $"/users/{userConfig.UserId}/calendar/events/{calendarEvent.EventId}");
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                    request.Content = new StringContent(JsonHelper.Serialize(calendarEvent.ToGraphEventPayload()),
                        Encoding.UTF8, new MediaTypeHeaderValue(JsonMediaType));

                    var response = await _httpClient.SendAsync(request, cancellationToken);

                    if (response.IsSuccessStatusCode)
                    {
                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;

                        _logger.LogInformation("Successfully updated event {EventId} for user {UserId}",
                            calendarEvent.EventId, userConfig.UserId);

                        return calendarEvent;
                    }
                    else
                    {
                        await HandleErrorResponseAsync(response, userConfig, cancellationToken);
                        // If HandleErrorResponseAsync didn't throw, we should still throw an exception
                        throw new GraphApiException($"Failed to update event {calendarEvent.EventId} for user {userConfig.UserId}",
                            (int)response.StatusCode);
                    }
                }
                catch (HttpRequestException ex)
                {
                    _logger.LogError(ex, "HTTP error occurred when updating event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);
                    throw new GraphApiException($"HTTP error updating event for user {userConfig.UserId}", null, ex);
                }
                catch (Exception ex) when (ex is not GraphApiException)
                {
                    _logger.LogError(ex, "Unexpected error updating event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);
                    throw new GraphApiException($"Unexpected error updating event for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Deletes a calendar event
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="eventId">The ID of the event to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the event was successfully deleted</returns>
        public async Task<bool> DeleteEventAsync(
            UserSyncConfig userConfig,
            string eventId,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (string.IsNullOrEmpty(eventId)) throw new ArgumentException("Event ID cannot be null or empty", nameof(eventId));

            string accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var request = new HttpRequestMessage(HttpMethod.Delete,
                        $"/users/{userConfig.UserId}/calendar/events/{eventId}");
                    request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

                    var response = await _httpClient.SendAsync(request, cancellationToken);

                    if (response.IsSuccessStatusCode)
                    {
                        _logger.LogInformation("Successfully deleted event {EventId} for user {UserId}",
                            eventId, userConfig.UserId);
                        return true;
                    }
                    else if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        // Event doesn't exist, but that's fine for a delete operation
                        _logger.LogWarning("Event {EventId} for user {UserId} not found during deletion (already deleted)",
                            eventId, userConfig.UserId);
                        return true;
                    }
                    else
                    {
                        await HandleErrorResponseAsync(response, userConfig, cancellationToken);
                        // If HandleErrorResponseAsync didn't throw, we should still throw an exception
                        throw new GraphApiException($"Failed to delete event {eventId} for user {userConfig.UserId}",
                            (int)response.StatusCode);
                    }
                }
                catch (HttpRequestException ex)
                {
                    _logger.LogError(ex, "HTTP error occurred when deleting event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);
                    throw new GraphApiException($"HTTP error deleting event for user {userConfig.UserId}", null, ex);
                }
                catch (Exception ex) when (ex is not GraphApiException)
                {
                    _logger.LogError(ex, "Unexpected error deleting event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);
                    throw new GraphApiException($"Unexpected error deleting event for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Processes multiple calendar operations in batch to optimize API usage
        /// </summary>
        /// <param name="userConfig">The user configuration</param>
        /// <param name="batchOperations">List of operations to perform</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Results of batch operations</returns>
        public async Task<BatchOperationResults> ProcessBatchAsync(
            UserSyncConfig userConfig,
            List<BatchOperation> batchOperations,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (batchOperations == null || !batchOperations.Any())
                throw new ArgumentException("Batch operations cannot be null or empty", nameof(batchOperations));

            string accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);
            var results = new BatchOperationResults();

            // Process in chunks to respect Graph API batch limits
            foreach (var chunk in batchOperations.Chunk(BatchRequestLimit))
            {
                var batchRequest = new
                {
                    requests = chunk.Select((op, i) => new
                    {
                        id = i.ToString(),
                        method = op.Method.ToString().ToUpper(),
                        url = op.Url,
                        body = op.Body,
                        headers = new Dictionary<string, string>
                        {
                            { "Content-Type", JsonMediaType }
                        }
                    }).ToArray()
                };

                var batchResults = await _graphApiPolicy.ExecuteAsync(async () =>
                {
                    try
                    {
                        var request = new HttpRequestMessage(HttpMethod.Post, "$batch");
                        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
                        request.Content = new StringContent(
                            JsonHelper.Serialize(batchRequest),
                            Encoding.UTF8,
                            new MediaTypeHeaderValue(JsonMediaType));

                        var response = await _httpClient.SendAsync(request, cancellationToken);

                        if (response.IsSuccessStatusCode)
                        {
                            var content = await response.Content.ReadAsStringAsync(cancellationToken);
                            var batchResponse = JsonHelper.Deserialize<Dictionary<string, object>>(content);

                            var responseItems = batchResponse["responses"].EnumerateArray();
                            var chunkResults = new List<BatchOperationResult>();

                            foreach (var responseItem in responseItems)
                            {
                                var id = int.Parse(responseItem.GetProperty("id").GetString());
                                var status = responseItem.GetProperty("status").GetInt32();
                                var body = responseItem.TryGetProperty("body", out var bodyElement)
                                    ? bodyElement.ToString()
                                    : null;

                                var operation = chunk.ElementAt(id);
                                var result = new BatchOperationResult
                                {
                                    OperationType = operation.OperationType,
                                    ItemId = operation.ItemId,
                                    IsSuccessful = status >= 200 && status < 300,
                                    StatusCode = status,
                                    Response = body
                                };

                                chunkResults.Add(result);
                            }

                            return chunkResults;
                        }
                        else
                        {
                            await HandleErrorResponseAsync(response, userConfig, cancellationToken);
                            // If HandleErrorResponseAsync didn't throw, we should still throw an exception
                            throw new GraphApiException($"Batch request failed for user {userConfig.UserId}",
                                (int)response.StatusCode);
                        }
                    }
                    catch (Exception ex) when (ex is not GraphApiException)
                    {
                        _logger.LogError(ex, "Error processing batch request for user {UserId}", userConfig.UserId);
                        throw new GraphApiException($"Error processing batch request for user {userConfig.UserId}", ex);
                    }
                });

                results.Results.AddRange(batchResults);
            }

            _logger.LogInformation("Processed {SuccessCount} successful operations out of {TotalCount} for user {UserId}",
                results.Results.Count(r => r.IsSuccessful),
                results.Results.Count,
                userConfig.UserId);

            return results;
        }

        /// <summary>
        /// Handles error responses from the Graph API, including token refreshing and authentication issues
        /// </summary>
        private async Task HandleErrorResponseAsync(
            HttpResponseMessage response,
            UserSyncConfig userConfig,
            CancellationToken cancellationToken)
        {
            string responseBody = await response.Content.ReadAsStringAsync(cancellationToken);

            // Try to parse the error response
            try
            {
                var errorResponse = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(responseBody);

                if (errorResponse.TryGetValue("error", out var errorObj))
                {
                    string errorCode = errorObj.GetProperty("code").GetString();
                    string errorMessage = errorObj.GetProperty("message").GetString();

                    _logger.LogError("Graph API error: {ErrorCode} - {ErrorMessage} for user {UserId}",
                        errorCode, errorMessage, userConfig.UserId);

                    // Handle authentication and token issues
                    if (errorCode == "InvalidAuthenticationToken" ||
                        errorCode == "AuthenticationFailed" ||
                        errorCode == "TokenExpired")
                    {
                        // Invalidate the token so it will be refreshed next time
                        _tokenManagementService.InvalidateToken(userConfig.UserId, userConfig.TenantId);
                        throw new TokenExpiredException($"Token expired or invalid for user {userConfig.UserId}");
                    }

                    // Handle throttling
                    if (errorCode == "TooManyRequests")
                    {
                        // Extract retry-after if available
                        if (response.Headers.TryGetValues("Retry-After", out var values))
                        {
                            int retryAfterSeconds = int.Parse(values.First());
                            throw new ThrottlingException(
                                $"Rate limited by Graph API for user {userConfig.UserId}. Retry after {retryAfterSeconds} seconds.",
                                retryAfterSeconds);
                        }
                        else
                        {
                            throw new ThrottlingException($"Rate limited by Graph API for user {userConfig.UserId}");
                        }
                    }

                    // Handle permission issues
                    if (errorCode == "AccessDenied" || errorCode == "Forbidden" ||
                        response.StatusCode == HttpStatusCode.Forbidden)
                    {
                        throw new PermissionException(
                            $"Permission denied by Graph API for user {userConfig.UserId}: {errorMessage}");
                    }

                    // Handle resource not found
                    if (errorCode == "ResourceNotFound" || response.StatusCode == HttpStatusCode.NotFound)
                    {
                        throw new ResourceNotFoundException(
                            $"Resource not found in Graph API for user {userConfig.UserId}: {errorMessage}");
                    }

                    // Handle general bad requests
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        throw new GraphApiException(
                            $"Bad request to Graph API for user {userConfig.UserId}: {errorMessage}",
                            (int)response.StatusCode, isTransient: false);
                    }
                }
            }
            catch (JsonException)
            {
                // If we can't parse the error, just log the raw response
                _logger.LogError("Failed to parse Graph API error response: {ResponseBody}", responseBody);
            }

            // For server errors, assume they're transient
            if ((int)response.StatusCode >= 500)
            {
                throw new GraphApiException(
                    $"Server error from Graph API for user {userConfig.UserId}: {response.StatusCode}",
                    (int)response.StatusCode, isTransient: true);
            }

            // Default error handling
            throw new GraphApiException(
                $"Graph API request failed for user {userConfig.UserId} with status code {response.StatusCode}",
                (int)response.StatusCode);
        }
    }

    /// <summary>
    /// Represents a batch operation for the Microsoft Graph API
    /// </summary>
    public class BatchOperation
    {
        /// <summary>
        /// HTTP method for the operation
        /// </summary>
        public HttpMethod Method { get; set; }

        /// <summary>
        /// Relative URL for the operation
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// Request body (for POST/PATCH operations)
        /// </summary>
        public object Body { get; set; }

        /// <summary>
        /// Type of operation being performed
        /// </summary>
        public BatchOperationType OperationType { get; set; }

        /// <summary>
        /// ID of the item being operated on
        /// </summary>
        public string ItemId { get; set; }
    }

    /// <summary>
    /// Types of operations that can be performed in a batch
    /// </summary>
    public enum BatchOperationType
    {
        Create,
        Update,
        Delete,
        Get
    }

    /// <summary>
    /// Results of batch operations
    /// </summary>
    public class BatchOperationResults
    {
        public List<BatchOperationResult> Results { get; set; } = new List<BatchOperationResult>();

        public IEnumerable<BatchOperationResult> SuccessfulResults => Results.Where(r => r.IsSuccessful);

        public IEnumerable<BatchOperationResult> FailedResults => Results.Where(r => !r.IsSuccessful);
    }

    /// <summary>
    /// Result of a single batch operation
    /// </summary>
    public class BatchOperationResult
    {
        public BatchOperationType OperationType { get; set; }

        public string ItemId { get; set; }

        public bool IsSuccessful { get; set; }

        public int StatusCode { get; set; }

        public string Response { get; set; }
    }
}
