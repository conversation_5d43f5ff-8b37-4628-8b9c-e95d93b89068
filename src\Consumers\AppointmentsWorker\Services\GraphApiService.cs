using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Graph;
using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Polly;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Extensions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Service for interacting with Microsoft Graph API for calendar operations using Graph SDK
    /// </summary>
    public class GraphApiService
    {
        private readonly ILogger<GraphApiService> _logger;
        private readonly TokenManagementService _tokenManagementService;
        private readonly IAsyncPolicy _graphApiPolicy;

        public GraphApiService(
            ILogger<GraphApiService> logger,
            TokenManagementService tokenManagementService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenManagementService = tokenManagementService ?? throw new ArgumentNullException(nameof(tokenManagementService));

            // Set up resilience policy for Graph API operations
            _graphApiPolicy = ResiliencePolicies.CreateGraphApiPolicy(logger);
        }

        /// <summary>
        /// Creates a single calendar event (uses batch internally for consistency)
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created event with server-side properties populated</returns>
        public async Task<CalendarEventWrapper> CreateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            var results = await CreateEventsAsync(userConfig, new[] { calendarEvent }, cancellationToken);
            return results.First();
        }

        /// <summary>
        /// Creates multiple calendar events using Graph batch requests
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvents">The calendar events to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created events with server-side properties populated</returns>
        public async Task<IEnumerable<CalendarEventWrapper>> CreateEventsAsync(
            UserSyncConfig userConfig,
            IEnumerable<CalendarEventWrapper> calendarEvents,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvents == null) throw new ArgumentNullException(nameof(calendarEvents));

            var eventsList = calendarEvents.ToList();
            if (!eventsList.Any()) return Enumerable.Empty<CalendarEventWrapper>();

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                _logger.LogInformation("Creating {Count} calendar events for user {UserId} in tenant {TenantId}",
                    eventsList.Count, userConfig.UserId, userConfig.FirmId);

                // Process in batches of 20 (Graph API limit) using LINQ
                const int batchSize = 20;
                var batchTasks = eventsList
                    .Select((item, index) => new { item, index })
                    .GroupBy(x => x.index / batchSize)
                    .Select(group => group.Select(x => x.item).ToList())
                    .Select(batch => ProcessCreateBatchAsync(graphServiceClient, userConfig, batch, cancellationToken));

                var batchResults = await Task.WhenAll(batchTasks);
                return batchResults.SelectMany(result => result);
            });
        }

        /// <summary>
        /// Updates a single calendar event (uses batch internally for consistency)
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated event</returns>
        public async Task<CalendarEventWrapper> UpdateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            var results = await UpdateEventsAsync(userConfig, new[] { calendarEvent }, cancellationToken);
            return results.First();
        }

        /// <summary>
        /// Updates multiple calendar events using Graph batch requests
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvents">The calendar events to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated events</returns>
        public async Task<IEnumerable<CalendarEventWrapper>> UpdateEventsAsync(
            UserSyncConfig userConfig,
            IEnumerable<CalendarEventWrapper> calendarEvents,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvents == null) throw new ArgumentNullException(nameof(calendarEvents));

            var eventsList = calendarEvents.ToList();
            if (!eventsList.Any()) return Enumerable.Empty<CalendarEventWrapper>();

            // Validate all events have EventIds using LINQ
            var eventsWithoutIds = eventsList.Where(e => string.IsNullOrEmpty(e.EventId));
            if (eventsWithoutIds.Any())
            {
                var missingCount = eventsWithoutIds.Count();
                throw new ArgumentException($"EventId is required for update operations. {missingCount} events missing EventId.");
            }

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                _logger.LogInformation("Updating {Count} calendar events for user {UserId} in tenant {TenantId}",
                    eventsList.Count, userConfig.UserId, userConfig.FirmId);

                // Process in batches of 20 (Graph API limit) using LINQ
                const int batchSize = 20;
                var batchTasks = eventsList
                    .Select((item, index) => new { item, index })
                    .GroupBy(x => x.index / batchSize)
                    .Select(group => group.Select(x => x.item).ToList())
                    .Select(batch => ProcessUpdateBatchAsync(graphServiceClient, userConfig, batch, cancellationToken));

                var batchResults = await Task.WhenAll(batchTasks);
                return batchResults.SelectMany(result => result);
            });
        }

        /// <summary>
        /// Deletes a single calendar event (uses batch internally for consistency)
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="eventId">The ID of the event to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the event was successfully deleted</returns>
        public async Task<bool> DeleteEventAsync(
            UserSyncConfig userConfig,
            string eventId,
            CancellationToken cancellationToken)
        {
            var results = await DeleteEventsAsync(userConfig, new[] { eventId }, cancellationToken);
            return results.First().Value;
        }

        /// <summary>
        /// Deletes multiple calendar events using Graph batch requests
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="eventIds">The IDs of the events to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dictionary of eventId -> success status</returns>
        public async Task<Dictionary<string, bool>> DeleteEventsAsync(
            UserSyncConfig userConfig,
            IEnumerable<string> eventIds,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (eventIds == null) throw new ArgumentNullException(nameof(eventIds));

            var eventIdsList = eventIds.Where(id => !string.IsNullOrEmpty(id)).ToList();
            if (!eventIdsList.Any()) return new Dictionary<string, bool>();

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                _logger.LogInformation("Deleting {Count} calendar events for user {UserId} in tenant {TenantId}",
                    eventIdsList.Count, userConfig.UserId, userConfig.FirmId);

                // Process in batches of 20 (Graph API limit) using LINQ
                const int batchSize = 20;
                var batchTasks = eventIdsList
                    .Select((item, index) => new { item, index })
                    .GroupBy(x => x.index / batchSize)
                    .Select(group => group.Select(x => x.item).ToList())
                    .Select(batch => ProcessDeleteBatchAsync(graphServiceClient, userConfig, batch, cancellationToken));

                var batchResults = await Task.WhenAll(batchTasks);
                return batchResults
                    .SelectMany(dict => dict)
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            });
        }

        /// <summary>
        /// Processes a batch of create operations using parallel individual requests
        /// (Graph SDK v5 batch API is complex, so using optimized parallel approach)
        /// </summary>
        private async Task<List<CalendarEventWrapper>> ProcessCreateBatchAsync(
            GraphServiceClient graphServiceClient,
            UserSyncConfig userConfig,
            List<CalendarEventWrapper> events,
            CancellationToken cancellationToken)
        {
            // Local function for creating individual events
            async Task<CalendarEventWrapper> CreateEventAsync(CalendarEventWrapper calendarEvent)
            {
                try
                {
                    var createdEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events
                        .PostAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                    return createdEvent?.Id != null
                        ? calendarEvent.WithSuccess(createdEvent.Id, DateTimeOffset.UtcNow)
                        : calendarEvent.WithFailure("No event ID returned from Graph API");
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Failed to create event for user {UserId}: {ErrorCode}",
                        userConfig.UserId, ex.ResponseStatusCode);

                    return calendarEvent.WithFailure($"Graph API error: {ex.ResponseStatusCode} - {ex.Message}");
                }
            }

            // For small batches (1-3 items), use unlimited parallelism
            if (events.Count <= 3)
            {
                var smallBatchResults = await Task.WhenAll(events.Select(CreateEventAsync));
                return smallBatchResults.ToList();
            }

            // For larger batches, use throttled parallelism with SemaphoreSlim
            using var semaphore = new SemaphoreSlim(5, 5);

            var throttledTasks = events.Select(async calendarEvent =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    return await CreateEventAsync(calendarEvent);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var largeBatchResults = await Task.WhenAll(throttledTasks);
            return largeBatchResults.ToList();
        }

        /// <summary>
        /// Processes a batch of update operations using parallel individual requests
        /// </summary>
        private async Task<List<CalendarEventWrapper>> ProcessUpdateBatchAsync(
            GraphServiceClient graphServiceClient,
            UserSyncConfig userConfig,
            List<CalendarEventWrapper> events,
            CancellationToken cancellationToken)
        {
            // Local function for updating individual events
            async Task<CalendarEventWrapper> UpdateEventAsync(CalendarEventWrapper calendarEvent)
            {
                try
                {
                    await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events[calendarEvent.EventId]
                        .PatchAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                    _logger.LogDebug("Successfully updated event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);

                    return calendarEvent.WithUpdateSuccess(DateTimeOffset.UtcNow);
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Failed to update event {EventId} for user {UserId}: {ErrorCode}",
                        calendarEvent.EventId, userConfig.UserId, ex.ResponseStatusCode);

                    return calendarEvent.WithFailure($"Graph API error: {ex.ResponseStatusCode} - {ex.Message}");
                }
            }

            // For small batches (1-3 items), use unlimited parallelism
            if (events.Count <= 3)
            {
                var smallBatchResults = await Task.WhenAll(events.Select(UpdateEventAsync));
                return smallBatchResults.ToList();
            }

            // For larger batches, use throttled parallelism with SemaphoreSlim
            using var semaphore = new SemaphoreSlim(5, 5);

            var throttledTasks = events.Select(async calendarEvent =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    return await UpdateEventAsync(calendarEvent);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var largeBatchResults = await Task.WhenAll(throttledTasks);
            return largeBatchResults.ToList();
        }

        /// <summary>
        /// Processes a batch of delete operations using parallel individual requests
        /// </summary>
        private async Task<Dictionary<string, bool>> ProcessDeleteBatchAsync(
            GraphServiceClient graphServiceClient,
            UserSyncConfig userConfig,
            List<string> eventIds,
            CancellationToken cancellationToken)
        {
            // Local function for deleting individual events
            async Task<KeyValuePair<string, bool>> DeleteEventAsync(string eventId)
            {
                try
                {
                    await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events[eventId]
                        .DeleteAsync(requestConfiguration: null, cancellationToken);

                    _logger.LogDebug("Successfully deleted event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    return new KeyValuePair<string, bool>(eventId, true);
                }
                catch (ServiceException ex) when (ex.ResponseStatusCode == 404)
                {
                    // Event doesn't exist, but that's fine for a delete operation
                    _logger.LogWarning("Event {EventId} for user {UserId} not found during deletion (already deleted)",
                        eventId, userConfig.UserId);
                    return new KeyValuePair<string, bool>(eventId, true);
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Failed to delete event {EventId} for user {UserId}: {ErrorCode}",
                        eventId, userConfig.UserId, ex.ResponseStatusCode);
                    return new KeyValuePair<string, bool>(eventId, false);
                }
            }

            // For small batches (1-3 items), use unlimited parallelism
            if (eventIds.Count <= 3)
            {
                var smallBatchResults = await Task.WhenAll(eventIds.Select(DeleteEventAsync));
                return smallBatchResults.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            }

            // For larger batches, use throttled parallelism with SemaphoreSlim
            using var semaphore = new SemaphoreSlim(5, 5);

            var throttledTasks = eventIds.Select(async eventId =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    return await DeleteEventAsync(eventId);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var largeBatchResults = await Task.WhenAll(throttledTasks);
            return largeBatchResults.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Creates a GraphServiceClient with the user's access token
        /// </summary>
        private async Task<GraphServiceClient> CreateGraphServiceClientAsync(
            UserSyncConfig userConfig,
            CancellationToken cancellationToken)
        {
            var accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);

            if (string.IsNullOrEmpty(accessToken))
            {
                throw new UnauthorizedAccessException($"Unable to obtain valid access token for user {userConfig.UserId}");
            }

            // Create a simple authentication provider that uses our token
            var authProvider = new SimpleAuthenticationProvider(accessToken);
            return new GraphServiceClient(authProvider);
        }

        /// <summary>
        /// Simple authentication provider for Graph SDK
        /// </summary>
        private class SimpleAuthenticationProvider : IAuthenticationProvider
        {
            private readonly string _accessToken;

            public SimpleAuthenticationProvider(string accessToken)
            {
                _accessToken = accessToken;
            }

            public Task AuthenticateRequestAsync(RequestInformation request, Dictionary<string, object>? additionalAuthenticationContext = null, CancellationToken cancellationToken = default)
            {
                request.Headers.Add("Authorization", $"Bearer {_accessToken}");
                return Task.CompletedTask;
            }
        }
    }
}
