using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Polly;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Service for interacting with Microsoft Graph API for calendar operations using Graph SDK
    /// </summary>
    public class GraphApiService
    {
        private readonly ILogger<GraphApiService> _logger;
        private readonly TokenManagementService _tokenManagementService;
        private readonly IAsyncPolicy _graphApiPolicy;

        public GraphApiService(
            ILogger<GraphApiService> logger,
            TokenManagementService tokenManagementService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenManagementService = tokenManagementService ?? throw new ArgumentNullException(nameof(tokenManagementService));

            // Set up resilience policy for Graph API operations
            _graphApiPolicy = ResiliencePolicies.CreateGraphApiPolicy(logger);
        }

        /// <summary>
        /// Creates a calendar event in the user's default calendar using Graph SDK
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created event with server-side properties populated</returns>
        public async Task<CalendarEventWrapper> CreateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvent == null) throw new ArgumentNullException(nameof(calendarEvent));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Creating calendar event for user {UserId} in tenant {TenantId}",
                        userConfig.UserId, userConfig.FirmId);

                    var createdEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events
                        .PostAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                    if (createdEvent?.Id != null)
                    {
                        calendarEvent.EventId = createdEvent.Id;
                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;

                        _logger.LogInformation("Successfully created event {EventId} for user {UserId}",
                            createdEvent.Id, userConfig.UserId);
                    }

                    return calendarEvent;
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Graph API error occurred when creating event for user {UserId}: {ErrorCode}",
                        userConfig.UserId, ex.ResponseStatusCode);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}";

                    throw new GraphApiException($"Graph API error creating event for user {userConfig.UserId}: {ex.ResponseStatusCode}",
                        ex.ResponseStatusCode, ex);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when creating event for user {UserId}", userConfig.UserId);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Unexpected error: {ex.Message}";

                    throw new GraphApiException($"Error creating event for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Updates an existing calendar event using Graph SDK
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated event</returns>
        public async Task<CalendarEventWrapper> UpdateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvent == null) throw new ArgumentNullException(nameof(calendarEvent));
            if (string.IsNullOrEmpty(calendarEvent.EventId))
                throw new ArgumentException("EventId is required for update operations", nameof(calendarEvent));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Updating calendar event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);

                    var updatedEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events[calendarEvent.EventId]
                        .PatchAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                    if (updatedEvent != null)
                    {
                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;
                        calendarEvent.ModifiedAt = DateTimeOffset.UtcNow;

                        _logger.LogInformation("Successfully updated event {EventId} for user {UserId}",
                            calendarEvent.EventId, userConfig.UserId);
                    }

                    return calendarEvent;
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Graph API error occurred when updating event {EventId} for user {UserId}: {ErrorCode}",
                        calendarEvent.EventId, userConfig.UserId, ex.ResponseStatusCode);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}";

                    throw new GraphApiException($"Graph API error updating event {calendarEvent.EventId} for user {userConfig.UserId}: {ex.ResponseStatusCode}",
                        ex.ResponseStatusCode, ex);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when updating event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Unexpected error: {ex.Message}";

                    throw new GraphApiException($"Error updating event {calendarEvent.EventId} for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Deletes a calendar event using Graph SDK
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="eventId">The ID of the event to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the event was successfully deleted</returns>
        public async Task<bool> DeleteEventAsync(
            UserSyncConfig userConfig,
            string eventId,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (string.IsNullOrEmpty(eventId)) throw new ArgumentException("EventId cannot be null or empty", nameof(eventId));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Deleting calendar event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events[eventId]
                        .DeleteAsync(requestConfiguration: null, cancellationToken);

                    _logger.LogInformation("Successfully deleted event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    return true;
                }
                catch (ServiceException ex) when (ex.ResponseStatusCode == 404)
                {
                    // Event doesn't exist, but that's fine for a delete operation
                    _logger.LogWarning("Event {EventId} for user {UserId} not found during deletion (already deleted)",
                        eventId, userConfig.UserId);
                    return true;
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Graph API error occurred when deleting event {EventId} for user {UserId}: {ErrorCode}",
                        eventId, userConfig.UserId, ex.ResponseStatusCode);

                    throw new GraphApiException($"Graph API error deleting event {eventId} for user {userConfig.UserId}: {ex.ResponseStatusCode}",
                        ex.ResponseStatusCode, ex);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when deleting event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    throw new GraphApiException($"Error deleting event {eventId} for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Creates a GraphServiceClient with the user's access token
        /// </summary>
        private async Task<GraphServiceClient> CreateGraphServiceClientAsync(
            UserSyncConfig userConfig,
            CancellationToken cancellationToken)
        {
            var accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);

            if (string.IsNullOrEmpty(accessToken))
            {
                throw new UnauthorizedAccessException($"Unable to obtain valid access token for user {userConfig.UserId}");
            }

            // Create a simple authentication provider that uses our token
            var authProvider = new SimpleAuthenticationProvider(accessToken);
            return new GraphServiceClient(authProvider);
        }

        /// <summary>
        /// Simple authentication provider for Graph SDK
        /// </summary>
        private class SimpleAuthenticationProvider : IAuthenticationProvider
        {
            private readonly string _accessToken;

            public SimpleAuthenticationProvider(string accessToken)
            {
                _accessToken = accessToken;
            }

            public Task AuthenticateRequestAsync(RequestInformation request, Dictionary<string, object>? additionalAuthenticationContext = null, CancellationToken cancellationToken = default)
            {
                request.Headers.Add("Authorization", $"Bearer {_accessToken}");
                return Task.CompletedTask;
            }
        }
    }
}
