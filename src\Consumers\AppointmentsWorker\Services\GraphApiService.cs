using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Kiota.Abstractions;
using Microsoft.Kiota.Abstractions.Authentication;
using Polly;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Service for interacting with Microsoft Graph API for calendar operations using Graph SDK
    /// </summary>
    public class GraphApiService
    {
        private readonly ILogger<GraphApiService> _logger;
        private readonly TokenManagementService _tokenManagementService;
        private readonly IAsyncPolicy _graphApiPolicy;

        public GraphApiService(
            ILogger<GraphApiService> logger,
            TokenManagementService tokenManagementService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenManagementService = tokenManagementService ?? throw new ArgumentNullException(nameof(tokenManagementService));

            // Set up resilience policy for Graph API operations
            _graphApiPolicy = ResiliencePolicies.CreateGraphApiPolicy(logger);
        }

        /// <summary>
        /// Creates a single calendar event (uses batch internally for consistency)
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created event with server-side properties populated</returns>
        public async Task<CalendarEventWrapper> CreateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            var results = await CreateEventsAsync(userConfig, new[] { calendarEvent }, cancellationToken);
            return results.First();
        }

        /// <summary>
        /// Creates multiple calendar events using Graph batch requests
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvents">The calendar events to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created events with server-side properties populated</returns>
        public async Task<IEnumerable<CalendarEventWrapper>> CreateEventsAsync(
            UserSyncConfig userConfig,
            IEnumerable<CalendarEventWrapper> calendarEvents,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvents == null) throw new ArgumentNullException(nameof(calendarEvents));

            var eventsList = calendarEvents.ToList();
            if (!eventsList.Any()) return new List<CalendarEventWrapper>();

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                _logger.LogInformation("Creating {Count} calendar events for user {UserId} in tenant {TenantId}",
                    eventsList.Count, userConfig.UserId, userConfig.FirmId);

                // Process in batches of 20 (Graph API limit)
                const int batchSize = 20;
                var results = new List<CalendarEventWrapper>();

                for (int i = 0; i < eventsList.Count; i += batchSize)
                {
                    var batch = eventsList.Skip(i).Take(batchSize).ToList();
                    var batchResults = await ProcessCreateBatchAsync(graphServiceClient, userConfig, batch, cancellationToken);
                    results.AddRange(batchResults);
                }

                return results;
            });
        }

        /// <summary>
        /// Updates a single calendar event (uses batch internally for consistency)
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated event</returns>
        public async Task<CalendarEventWrapper> UpdateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            var results = await UpdateEventsAsync(userConfig, new[] { calendarEvent }, cancellationToken);
            return results.First();
        }

        /// <summary>
        /// Updates multiple calendar events using Graph batch requests
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvents">The calendar events to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated events</returns>
        public async Task<IEnumerable<CalendarEventWrapper>> UpdateEventsAsync(
            UserSyncConfig userConfig,
            IEnumerable<CalendarEventWrapper> calendarEvents,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvents == null) throw new ArgumentNullException(nameof(calendarEvents));

            var eventsList = calendarEvents.ToList();
            if (!eventsList.Any()) return new List<CalendarEventWrapper>();

            // Validate all events have EventIds
            var eventsWithoutIds = eventsList.Where(e => string.IsNullOrEmpty(e.EventId)).ToList();
            if (eventsWithoutIds.Any())
                throw new ArgumentException($"EventId is required for update operations. {eventsWithoutIds.Count} events missing EventId.");

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                _logger.LogInformation("Updating {Count} calendar events for user {UserId} in tenant {TenantId}",
                    eventsList.Count, userConfig.UserId, userConfig.FirmId);

                // Process in batches of 20 (Graph API limit)
                const int batchSize = 20;
                var results = new List<CalendarEventWrapper>();

                for (int i = 0; i < eventsList.Count; i += batchSize)
                {
                    var batch = eventsList.Skip(i).Take(batchSize).ToList();
                    var batchResults = await ProcessUpdateBatchAsync(graphServiceClient, userConfig, batch, cancellationToken);
                    results.AddRange(batchResults);
                }

                return results;
            });
        }

        /// <summary>
        /// Deletes a single calendar event (uses batch internally for consistency)
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="eventId">The ID of the event to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the event was successfully deleted</returns>
        public async Task<bool> DeleteEventAsync(
            UserSyncConfig userConfig,
            string eventId,
            CancellationToken cancellationToken)
        {
            var results = await DeleteEventsAsync(userConfig, new[] { eventId }, cancellationToken);
            return results.First().Value;
        }

        /// <summary>
        /// Deletes multiple calendar events using Graph batch requests
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="eventIds">The IDs of the events to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dictionary of eventId -> success status</returns>
        public async Task<Dictionary<string, bool>> DeleteEventsAsync(
            UserSyncConfig userConfig,
            IEnumerable<string> eventIds,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (eventIds == null) throw new ArgumentNullException(nameof(eventIds));

            var eventIdsList = eventIds.Where(id => !string.IsNullOrEmpty(id)).ToList();
            if (!eventIdsList.Any()) return new Dictionary<string, bool>();

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                _logger.LogInformation("Deleting {Count} calendar events for user {UserId} in tenant {TenantId}",
                    eventIdsList.Count, userConfig.UserId, userConfig.FirmId);

                // Process in batches of 20 (Graph API limit)
                const int batchSize = 20;
                var results = new Dictionary<string, bool>();

                for (int i = 0; i < eventIdsList.Count; i += batchSize)
                {
                    var batch = eventIdsList.Skip(i).Take(batchSize).ToList();
                    var batchResults = await ProcessDeleteBatchAsync(graphServiceClient, userConfig, batch, cancellationToken);

                    foreach (var result in batchResults)
                    {
                        results[result.Key] = result.Value;
                    }
                }

                return results;
            });
        }

        /// <summary>
        /// Processes a batch of create operations using parallel individual requests
        /// (Graph SDK v5 batch API is complex, so using optimized parallel approach)
        /// </summary>
        private async Task<List<CalendarEventWrapper>> ProcessCreateBatchAsync(
            GraphServiceClient graphServiceClient,
            UserSyncConfig userConfig,
            List<CalendarEventWrapper> events,
            CancellationToken cancellationToken)
        {
            // For small batches (1-3 items), use parallel individual requests
            // This is often faster than batch overhead for small sets
            if (events.Count <= 3)
            {
                var smallBatchTasks = events.Select(async calendarEvent =>
                {
                    try
                    {
                        var createdEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                            .Calendar
                            .Events
                            .PostAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                        if (createdEvent?.Id != null)
                        {
                            calendarEvent.EventId = createdEvent.Id;
                            calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                            calendarEvent.SyncStatus = SyncStatus.Synced;

                            _logger.LogDebug("Successfully created event {EventId} for user {UserId}",
                                createdEvent.Id, userConfig.UserId);
                        }
                        return calendarEvent;
                    }
                    catch (ServiceException ex)
                    {
                        _logger.LogError(ex, "Failed to create event for user {UserId}: {ErrorCode}",
                            userConfig.UserId, ex.ResponseStatusCode);

                        calendarEvent.SyncStatus = SyncStatus.Failed;
                        calendarEvent.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}";
                        return calendarEvent;
                    }
                });

                return (await Task.WhenAll(smallBatchTasks)).ToList();
            }

            // For larger batches, use sequential processing with throttling
            // This prevents overwhelming the Graph API
            var semaphore = new SemaphoreSlim(5, 5); // Max 5 concurrent requests

            var largeBatchTasks = events.Select(async calendarEvent =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    var createdEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events
                        .PostAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                    if (createdEvent?.Id != null)
                    {
                        calendarEvent.EventId = createdEvent.Id;
                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;

                        _logger.LogDebug("Successfully created event {EventId} for user {UserId}",
                            createdEvent.Id, userConfig.UserId);
                    }
                    return calendarEvent;
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Failed to create event for user {UserId}: {ErrorCode}",
                        userConfig.UserId, ex.ResponseStatusCode);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}";
                    return calendarEvent;
                }
                finally
                {
                    semaphore.Release();
                }
            });

            return (await Task.WhenAll(largeBatchTasks)).ToList();
        }

        /// <summary>
        /// Processes a batch of update operations using parallel individual requests
        /// </summary>
        private async Task<List<CalendarEventWrapper>> ProcessUpdateBatchAsync(
            GraphServiceClient graphServiceClient,
            UserSyncConfig userConfig,
            List<CalendarEventWrapper> events,
            CancellationToken cancellationToken)
        {
            // For small batches (1-3 items), use parallel individual requests
            if (events.Count <= 3)
            {
                var smallBatchTasks = events.Select(async calendarEvent =>
                {
                    try
                    {
                        var updatedEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                            .Calendar
                            .Events[calendarEvent.EventId]
                            .PatchAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;
                        calendarEvent.ModifiedAt = DateTimeOffset.UtcNow;

                        _logger.LogDebug("Successfully updated event {EventId} for user {UserId}",
                            calendarEvent.EventId, userConfig.UserId);

                        return calendarEvent;
                    }
                    catch (ServiceException ex)
                    {
                        _logger.LogError(ex, "Failed to update event {EventId} for user {UserId}: {ErrorCode}",
                            calendarEvent.EventId, userConfig.UserId, ex.ResponseStatusCode);

                        calendarEvent.SyncStatus = SyncStatus.Failed;
                        calendarEvent.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}";
                        return calendarEvent;
                    }
                });

                return (await Task.WhenAll(smallBatchTasks)).ToList();
            }

            // For larger batches, use throttled parallel processing
            var semaphore = new SemaphoreSlim(5, 5); // Max 5 concurrent requests

            var largeBatchTasks = events.Select(async calendarEvent =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    var updatedEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events[calendarEvent.EventId]
                        .PatchAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                    calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                    calendarEvent.SyncStatus = SyncStatus.Synced;
                    calendarEvent.ModifiedAt = DateTimeOffset.UtcNow;

                    _logger.LogDebug("Successfully updated event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);

                    return calendarEvent;
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Failed to update event {EventId} for user {UserId}: {ErrorCode}",
                        calendarEvent.EventId, userConfig.UserId, ex.ResponseStatusCode);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}";
                    return calendarEvent;
                }
                finally
                {
                    semaphore.Release();
                }
            });

            return (await Task.WhenAll(largeBatchTasks)).ToList();
        }

        /// <summary>
        /// Processes a batch of delete operations using parallel individual requests
        /// </summary>
        private async Task<Dictionary<string, bool>> ProcessDeleteBatchAsync(
            GraphServiceClient graphServiceClient,
            UserSyncConfig userConfig,
            List<string> eventIds,
            CancellationToken cancellationToken)
        {
            // For small batches (1-3 items), use parallel individual requests
            if (eventIds.Count <= 3)
            {
                var smallBatchTasks = eventIds.Select(async eventId =>
                {
                    try
                    {
                        await graphServiceClient.Users[userConfig.UserId.ToString()]
                            .Calendar
                            .Events[eventId]
                            .DeleteAsync(requestConfiguration: null, cancellationToken);

                        _logger.LogDebug("Successfully deleted event {EventId} for user {UserId}",
                            eventId, userConfig.UserId);

                        return new KeyValuePair<string, bool>(eventId, true);
                    }
                    catch (ServiceException ex) when (ex.ResponseStatusCode == 404)
                    {
                        // Event doesn't exist, but that's fine for a delete operation
                        _logger.LogWarning("Event {EventId} for user {UserId} not found during deletion (already deleted)",
                            eventId, userConfig.UserId);
                        return new KeyValuePair<string, bool>(eventId, true);
                    }
                    catch (ServiceException ex)
                    {
                        _logger.LogError(ex, "Failed to delete event {EventId} for user {UserId}: {ErrorCode}",
                            eventId, userConfig.UserId, ex.ResponseStatusCode);
                        return new KeyValuePair<string, bool>(eventId, false);
                    }
                });

                var results = await Task.WhenAll(smallBatchTasks);
                return results.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            }

            // For larger batches, use throttled parallel processing
            var semaphore = new SemaphoreSlim(5, 5); // Max 5 concurrent requests

            var largeBatchTasks = eventIds.Select(async eventId =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events[eventId]
                        .DeleteAsync(requestConfiguration: null, cancellationToken);

                    _logger.LogDebug("Successfully deleted event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    return new KeyValuePair<string, bool>(eventId, true);
                }
                catch (ServiceException ex) when (ex.ResponseStatusCode == 404)
                {
                    // Event doesn't exist, but that's fine for a delete operation
                    _logger.LogWarning("Event {EventId} for user {UserId} not found during deletion (already deleted)",
                        eventId, userConfig.UserId);
                    return new KeyValuePair<string, bool>(eventId, true);
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Failed to delete event {EventId} for user {UserId}: {ErrorCode}",
                        eventId, userConfig.UserId, ex.ResponseStatusCode);
                    return new KeyValuePair<string, bool>(eventId, false);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var largeResults = await Task.WhenAll(largeBatchTasks);
            return largeResults.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }

        /// <summary>
        /// Creates a GraphServiceClient with the user's access token
        /// </summary>
        private async Task<GraphServiceClient> CreateGraphServiceClientAsync(
            UserSyncConfig userConfig,
            CancellationToken cancellationToken)
        {
            var accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);

            if (string.IsNullOrEmpty(accessToken))
            {
                throw new UnauthorizedAccessException($"Unable to obtain valid access token for user {userConfig.UserId}");
            }

            // Create a simple authentication provider that uses our token
            var authProvider = new SimpleAuthenticationProvider(accessToken);
            return new GraphServiceClient(authProvider);
        }

        /// <summary>
        /// Simple authentication provider for Graph SDK
        /// </summary>
        private class SimpleAuthenticationProvider : IAuthenticationProvider
        {
            private readonly string _accessToken;

            public SimpleAuthenticationProvider(string accessToken)
            {
                _accessToken = accessToken;
            }

            public Task AuthenticateRequestAsync(RequestInformation request, Dictionary<string, object>? additionalAuthenticationContext = null, CancellationToken cancellationToken = default)
            {
                request.Headers.Add("Authorization", $"Bearer {_accessToken}");
                return Task.CompletedTask;
            }
        }
    }
}
