using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Graph.Models;
using Microsoft.Extensions.Logging;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Serialization;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Service for interacting with Microsoft Graph API for calendar operations using Graph SDK
    /// </summary>
    public class GraphApiService
    {
        private readonly ILogger<GraphApiService> _logger;
        private readonly TokenManagementService _tokenManagementService;
        private readonly IAsyncPolicy _graphApiPolicy;

        public GraphApiService(
            ILogger<GraphApiService> logger,
            TokenManagementService tokenManagementService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenManagementService = tokenManagementService ?? throw new ArgumentNullException(nameof(tokenManagementService));

            // Set up resilience policy for Graph API operations
            _graphApiPolicy = ResiliencePolicies.CreateGraphApiPolicy(logger);
        }

        /// <summary>
        /// Creates a calendar event in the user's default calendar using Graph SDK
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created event with server-side properties populated</returns>
        public async Task<CalendarEventWrapper> CreateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvent == null) throw new ArgumentNullException(nameof(calendarEvent));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Creating calendar event for user {UserId} in tenant {TenantId}",
                        userConfig.UserId, userConfig.FirmId);

                    var createdEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events
                        .PostAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                    if (createdEvent?.Id != null)
                    {
                        calendarEvent.EventId = createdEvent.Id;
                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;

                        _logger.LogInformation("Successfully created event {EventId} for user {UserId}",
                            createdEvent.Id, userConfig.UserId);
                    }

                    return calendarEvent;
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Graph API error occurred when creating event for user {UserId}: {ErrorCode}",
                        userConfig.UserId, ex.ResponseStatusCode);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}";

                    throw new GraphApiException($"Graph API error creating event for user {userConfig.UserId}: {ex.ResponseStatusCode}",
                        ex.ResponseStatusCode, ex);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when creating event for user {UserId}", userConfig.UserId);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Unexpected error: {ex.Message}";

                    throw new GraphApiException($"Error creating event for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Updates an existing calendar event using Graph SDK
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated event</returns>
        public async Task<CalendarEventWrapper> UpdateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvent == null) throw new ArgumentNullException(nameof(calendarEvent));
            if (string.IsNullOrEmpty(calendarEvent.EventId))
                throw new ArgumentException("EventId is required for update operations", nameof(calendarEvent));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Updating calendar event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);

                    var updatedEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events[calendarEvent.EventId]
                        .PatchAsync(calendarEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                    if (updatedEvent != null)
                    {
                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;
                        calendarEvent.ModifiedAt = DateTimeOffset.UtcNow;

                        _logger.LogInformation("Successfully updated event {EventId} for user {UserId}",
                            calendarEvent.EventId, userConfig.UserId);
                    }

                    return calendarEvent;
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Graph API error occurred when updating event {EventId} for user {UserId}: {ErrorCode}",
                        calendarEvent.EventId, userConfig.UserId, ex.ResponseStatusCode);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}";

                    throw new GraphApiException($"Graph API error updating event {calendarEvent.EventId} for user {userConfig.UserId}: {ex.ResponseStatusCode}",
                        ex.ResponseStatusCode, ex);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when updating event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Unexpected error: {ex.Message}";

                    throw new GraphApiException($"Error updating event {calendarEvent.EventId} for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Deletes a calendar event using Graph SDK
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="eventId">The ID of the event to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the event was successfully deleted</returns>
        public async Task<bool> DeleteEventAsync(
            UserSyncConfig userConfig,
            string eventId,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (string.IsNullOrEmpty(eventId)) throw new ArgumentException("EventId cannot be null or empty", nameof(eventId));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Deleting calendar event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    await graphServiceClient.Users[userConfig.UserId.ToString()]
                        .Calendar
                        .Events[eventId]
                        .DeleteAsync(requestConfiguration: null, cancellationToken);

                    _logger.LogInformation("Successfully deleted event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    return true;
                }
                catch (ServiceException ex) when (ex.ResponseStatusCode == 404)
                {
                    // Event doesn't exist, but that's fine for a delete operation
                    _logger.LogWarning("Event {EventId} for user {UserId} not found during deletion (already deleted)",
                        eventId, userConfig.UserId);
                    return true;
                }
                catch (ServiceException ex)
                {
                    _logger.LogError(ex, "Graph API error occurred when deleting event {EventId} for user {UserId}: {ErrorCode}",
                        eventId, userConfig.UserId, ex.ResponseStatusCode);

                    throw new GraphApiException($"Graph API error deleting event {eventId} for user {userConfig.UserId}: {ex.ResponseStatusCode}",
                        ex.ResponseStatusCode, ex);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when deleting event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    throw new GraphApiException($"Error deleting event {eventId} for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Processes multiple calendar events in batches using Graph SDK batch requests
        /// </summary>
        /// <param name="operations">The batch operations to process</param>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Results of the batch operations</returns>
        public async Task<BatchOperationResults> ProcessBatchAsync(
            IEnumerable<BatchOperation> operations,
            UserSyncConfig userConfig,
            CancellationToken cancellationToken)
        {
            if (operations == null) throw new ArgumentNullException(nameof(operations));
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));

            var operationsList = operations.ToList();
            if (!operationsList.Any())
                return new BatchOperationResults { Results = new List<BatchOperationResult>() };

            var results = new BatchOperationResults { Results = new List<BatchOperationResult>() };

            // Process operations in chunks of 20 (Graph API batch limit)
            const int batchSize = 20;
            var chunks = operationsList.Select((operation, index) => new { operation, index })
                                     .GroupBy(x => x.index / batchSize)
                                     .Select(g => g.Select(x => x.operation));

            foreach (var chunk in chunks)
            {
                var chunkResults = await ProcessBatchChunkAsync(chunk, userConfig, cancellationToken);
                results.Results.AddRange(chunkResults);
            }

            return results;
        }

        /// <summary>
        /// Processes a single chunk of batch operations
        /// </summary>
        private async Task<List<BatchOperationResult>> ProcessBatchChunkAsync(
            IEnumerable<BatchOperation> operations,
            UserSyncConfig userConfig,
            CancellationToken cancellationToken)
        {
            var results = new List<BatchOperationResult>();

            try
            {
                var graphServiceClient = await CreateGraphServiceClientAsync(userConfig, cancellationToken);

                // For now, process operations individually since Graph SDK batch support is complex
                // In a production environment, you would implement proper batch request handling
                foreach (var operation in operations)
                {
                    var result = await ProcessSingleOperationAsync(operation, graphServiceClient, userConfig, cancellationToken);
                    results.Add(result);
                }

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing batch operations for user {UserId}", userConfig.UserId);

                // Mark all operations as failed
                foreach (var operation in operations)
                {
                    results.Add(new BatchOperationResult
                    {
                        OperationType = operation.OperationType,
                        ItemId = operation.ItemId,
                        IsSuccessful = false,
                        StatusCode = 500,
                        Response = $"Batch processing error: {ex.Message}"
                    });
                }

                return results;
            }
        }

        /// <summary>
        /// Processes a single operation within a batch
        /// </summary>
        private async Task<BatchOperationResult> ProcessSingleOperationAsync(
            BatchOperation operation,
            GraphServiceClient graphServiceClient,
            UserSyncConfig userConfig,
            CancellationToken cancellationToken)
        {
            try
            {
                switch (operation.OperationType)
                {
                    case BatchOperationType.Create:
                        if (operation.Body is CalendarEventWrapper createEvent)
                        {
                            var createdEvent = await graphServiceClient.Users[userConfig.UserId.ToString()]
                                .Calendar
                                .Events
                                .PostAsync(createEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                            return new BatchOperationResult
                            {
                                OperationType = operation.OperationType,
                                ItemId = operation.ItemId,
                                IsSuccessful = true,
                                StatusCode = 201,
                                Response = createdEvent?.Id ?? string.Empty
                            };
                        }
                        break;

                    case BatchOperationType.Update:
                        if (operation.Body is CalendarEventWrapper updateEvent && !string.IsNullOrEmpty(updateEvent.EventId))
                        {
                            await graphServiceClient.Users[userConfig.UserId.ToString()]
                                .Calendar
                                .Events[updateEvent.EventId]
                                .PatchAsync(updateEvent.GraphEvent, requestConfiguration: null, cancellationToken);

                            return new BatchOperationResult
                            {
                                OperationType = operation.OperationType,
                                ItemId = operation.ItemId,
                                IsSuccessful = true,
                                StatusCode = 200,
                                Response = "Updated successfully"
                            };
                        }
                        break;

                    case BatchOperationType.Delete:
                        if (!string.IsNullOrEmpty(operation.ItemId))
                        {
                            await graphServiceClient.Users[userConfig.UserId.ToString()]
                                .Calendar
                                .Events[operation.ItemId]
                                .DeleteAsync(requestConfiguration: null, cancellationToken);

                            return new BatchOperationResult
                            {
                                OperationType = operation.OperationType,
                                ItemId = operation.ItemId,
                                IsSuccessful = true,
                                StatusCode = 204,
                                Response = "Deleted successfully"
                            };
                        }
                        break;
                }

                return new BatchOperationResult
                {
                    OperationType = operation.OperationType,
                    ItemId = operation.ItemId,
                    IsSuccessful = false,
                    StatusCode = 400,
                    Response = "Invalid operation data"
                };
            }
            catch (ServiceException ex)
            {
                return new BatchOperationResult
                {
                    OperationType = operation.OperationType,
                    ItemId = operation.ItemId,
                    IsSuccessful = false,
                    StatusCode = ex.ResponseStatusCode ?? 500,
                    Response = $"Graph API error: {ex.ResponseStatusCode} - {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                return new BatchOperationResult
                {
                    OperationType = operation.OperationType,
                    ItemId = operation.ItemId,
                    IsSuccessful = false,
                    StatusCode = 500,
                    Response = $"Unexpected error: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// Creates a GraphServiceClient with the user's access token
        /// </summary>
        private async Task<GraphServiceClient> CreateGraphServiceClientAsync(
            UserSyncConfig userConfig,
            CancellationToken cancellationToken)
        {
            var accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);

            if (string.IsNullOrEmpty(accessToken))
            {
                throw new UnauthorizedAccessException($"Unable to obtain valid access token for user {userConfig.UserId}");
            }

            // Create a simple HTTP client with the bearer token
            var httpClient = new System.Net.Http.HttpClient();
            httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            // Create Graph service client with the authenticated HTTP client
            var requestAdapter = new Microsoft.Graph.HttpClientRequestAdapter(
                new Microsoft.Graph.Authentication.AnonymousAuthenticationProvider(), httpClient);

            return new GraphServiceClient(requestAdapter);
        }
    }
}
