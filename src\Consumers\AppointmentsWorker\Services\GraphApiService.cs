using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Graph.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Serialization;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Service for interacting with Microsoft Graph API for calendar operations using HTTP client and Json.NET
    /// </summary>
    public class GraphApiService
    {
        private readonly ILogger<GraphApiService> _logger;
        private readonly TokenManagementService _tokenManagementService;
        private readonly HttpClient _httpClient;
        private readonly IAsyncPolicy _graphApiPolicy;

        // Constants
        private const string GraphApiEndpoint = "https://graph.microsoft.com/v1.0";
        private const string JsonMediaType = "application/json";

        public GraphApiService(
            ILogger<GraphApiService> logger,
            TokenManagementService tokenManagementService,
            IHttpClientFactory httpClientFactory)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenManagementService = tokenManagementService ?? throw new ArgumentNullException(nameof(tokenManagementService));
            _httpClient = httpClientFactory.CreateClient("GraphApi");
            _httpClient.BaseAddress = new Uri(GraphApiEndpoint);
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue(JsonMediaType));

            // Set up resilience policy for Graph API operations
            _graphApiPolicy = ResiliencePolicies.CreateGraphApiPolicy(logger);
        }

        /// <summary>
        /// Creates a calendar event in the user's default calendar using HTTP client and Json.NET
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to create</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The created event with server-side properties populated</returns>
        public async Task<CalendarEventWrapper> CreateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvent == null) throw new ArgumentNullException(nameof(calendarEvent));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    await SetAuthorizationHeaderAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Creating calendar event for user {UserId} in tenant {TenantId}",
                        userConfig.UserId, userConfig.FirmId);

                    var requestUrl = $"/users/{userConfig.UserId}/calendar/events";
                    var content = JsonHelper.ToJsonContent(calendarEvent.GraphEvent);

                    var response = await _httpClient.PostAsync(requestUrl, content, cancellationToken);

                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                        var createdEvent = JsonHelper.Deserialize<Event>(responseContent);

                        if (createdEvent?.Id != null)
                        {
                            calendarEvent.EventId = createdEvent.Id;
                            calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                            calendarEvent.SyncStatus = SyncStatus.Synced;

                            _logger.LogInformation("Successfully created event {EventId} for user {UserId}",
                                createdEvent.Id, userConfig.UserId);
                        }

                        return calendarEvent;
                    }
                    else
                    {
                        await HandleErrorResponseAsync(response, userConfig, cancellationToken);
                        throw new GraphApiException($"Failed to create event for user {userConfig.UserId}", (int)response.StatusCode);
                    }
                }
                catch (GraphApiException)
                {
                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when creating event for user {UserId}", userConfig.UserId);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Unexpected error: {ex.Message}";

                    throw new GraphApiException($"Error creating event for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Updates an existing calendar event using HTTP client and Json.NET
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="calendarEvent">The calendar event to update</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The updated event</returns>
        public async Task<CalendarEventWrapper> UpdateEventAsync(
            UserSyncConfig userConfig,
            CalendarEventWrapper calendarEvent,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (calendarEvent == null) throw new ArgumentNullException(nameof(calendarEvent));
            if (string.IsNullOrEmpty(calendarEvent.EventId))
                throw new ArgumentException("EventId is required for update operations", nameof(calendarEvent));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    await SetAuthorizationHeaderAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Updating calendar event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);

                    var requestUrl = $"/users/{userConfig.UserId}/calendar/events/{calendarEvent.EventId}";
                    var content = JsonHelper.ToJsonContent(calendarEvent.GraphEvent);

                    var response = await _httpClient.PatchAsync(requestUrl, content, cancellationToken);

                    if (response.IsSuccessStatusCode)
                    {
                        calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                        calendarEvent.SyncStatus = SyncStatus.Synced;
                        calendarEvent.ModifiedAt = DateTimeOffset.UtcNow;

                        _logger.LogInformation("Successfully updated event {EventId} for user {UserId}",
                            calendarEvent.EventId, userConfig.UserId);

                        return calendarEvent;
                    }
                    else
                    {
                        await HandleErrorResponseAsync(response, userConfig, cancellationToken);
                        throw new GraphApiException($"Failed to update event {calendarEvent.EventId} for user {userConfig.UserId}", (int)response.StatusCode);
                    }
                }
                catch (GraphApiException)
                {
                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when updating event {EventId} for user {UserId}",
                        calendarEvent.EventId, userConfig.UserId);

                    calendarEvent.SyncStatus = SyncStatus.Failed;
                    calendarEvent.SyncErrorDetails = $"Unexpected error: {ex.Message}";

                    throw new GraphApiException($"Error updating event {calendarEvent.EventId} for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Deletes a calendar event using HTTP client
        /// </summary>
        /// <param name="userConfig">The user configuration with token information</param>
        /// <param name="eventId">The ID of the event to delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if the event was successfully deleted</returns>
        public async Task<bool> DeleteEventAsync(
            UserSyncConfig userConfig,
            string eventId,
            CancellationToken cancellationToken)
        {
            if (userConfig == null) throw new ArgumentNullException(nameof(userConfig));
            if (string.IsNullOrEmpty(eventId)) throw new ArgumentException("EventId cannot be null or empty", nameof(eventId));

            return await _graphApiPolicy.ExecuteAsync(async () =>
            {
                try
                {
                    await SetAuthorizationHeaderAsync(userConfig, cancellationToken);

                    _logger.LogInformation("Deleting calendar event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    var requestUrl = $"/users/{userConfig.UserId}/calendar/events/{eventId}";
                    var response = await _httpClient.DeleteAsync(requestUrl, cancellationToken);

                    if (response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.NotFound)
                    {
                        _logger.LogInformation("Successfully deleted event {EventId} for user {UserId}",
                            eventId, userConfig.UserId);
                        return true;
                    }
                    else
                    {
                        await HandleErrorResponseAsync(response, userConfig, cancellationToken);
                        throw new GraphApiException($"Failed to delete event {eventId} for user {userConfig.UserId}", (int)response.StatusCode);
                    }
                }
                catch (GraphApiException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error occurred when deleting event {EventId} for user {UserId}",
                        eventId, userConfig.UserId);

                    throw new GraphApiException($"Error deleting event {eventId} for user {userConfig.UserId}", null, ex);
                }
            });
        }

        /// <summary>
        /// Sets the authorization header for the HTTP client
        /// </summary>
        private async Task SetAuthorizationHeaderAsync(UserSyncConfig userConfig, CancellationToken cancellationToken)
        {
            var accessToken = await _tokenManagementService.GetAccessTokenAsync(userConfig, cancellationToken);

            if (string.IsNullOrEmpty(accessToken))
            {
                throw new UnauthorizedAccessException($"Unable to obtain valid access token for user {userConfig.UserId}");
            }

            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Bearer", accessToken);
        }

        /// <summary>
        /// Handles error responses from the Graph API
        /// </summary>
        private async Task HandleErrorResponseAsync(HttpResponseMessage response, UserSyncConfig userConfig, CancellationToken cancellationToken)
        {
            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);

            _logger.LogError("Graph API error for user {UserId}: {StatusCode} - {ErrorContent}",
                userConfig.UserId, response.StatusCode, errorContent);

            // Try to parse the error response
            try
            {
                var errorResponse = JsonHelper.Deserialize<dynamic>(errorContent);
                var errorMessage = errorResponse?.error?.message?.ToString() ?? "Unknown error";

                throw new GraphApiException($"Graph API error for user {userConfig.UserId}: {errorMessage}",
                    (int)response.StatusCode);
            }
            catch (JsonException)
            {
                // If we can't parse the error, just throw with the raw content
                throw new GraphApiException($"Graph API error for user {userConfig.UserId}: {errorContent}",
                    (int)response.StatusCode);
            }
        }
    }
}
