using System;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions
{    /// <summary>
    /// Base exception for appointments consumer service
    /// </summary>
    public class AppointmentsConsumerException : Exception
    {
        /// <summary>
        /// Flag indicating if the exception is transient and can be retried
        /// </summary>
        public bool IsTransient { get; protected set; }

        /// <summary>
        /// Identifier of the tenant where the error occurred
        /// </summary>
        public string? TenantId { get; protected set; }

        /// <summary>
        /// Identifier of the user where the error occurred
        /// </summary>
        public string? UserId { get; protected set; }

        /// <summary>
        /// Error code for categorization and filtering
        /// </summary>
        public string ErrorCode { get; protected set; }

        /// <summary>
        /// Creates a new instance of AppointmentsConsumerException with the specified parameters
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="errorCode">Error code for categorization</param>
        /// <param name="isTransient">Whether the error is transient and can be retried</param>
        /// <param name="tenantId">Tenant identifier</param>
        /// <param name="userId">User identifier</param>
        /// <param name="innerException">Inner exception</param>
        public AppointmentsConsumerException(
            string message,
            string errorCode = "GENERAL_ERROR",
            bool isTransient = false,
            string? tenantId = null,
            string? userId = null,
            Exception? innerException = null)
            : base(message, innerException)
        {
            IsTransient = isTransient;
            TenantId = tenantId;
            UserId = userId;
            ErrorCode = errorCode;
        }
    }

    /// <summary>
    /// Exception for authentication failures with Microsoft Graph API
    /// </summary>
    public class GraphAuthenticationException : AppointmentsConsumerException
    {
        /// <summary>
        /// Creates a new instance of GraphAuthenticationException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="tenantId">Tenant identifier</param>
        /// <param name="userId">User identifier</param>
        /// <param name="isTransient">Whether the error is transient and can be retried</param>
        /// <param name="innerException">Inner exception</param>
        public GraphAuthenticationException(
            string message,
            string? tenantId = null,
            string? userId = null,
            bool isTransient = false,
            Exception? innerException = null)
            : base(message, "GRAPH_AUTH_ERROR", isTransient, tenantId, userId, innerException)
        {
        }
    }
      /// <summary>
    /// Exception for Graph API errors
    /// </summary>
    public class GraphApiException : AppointmentsConsumerException
    {
        /// <summary>
        /// HTTP status code if available
        /// </summary>
        public int? StatusCode { get; }

        public GraphApiException(string message, int? statusCode = null, Exception? innerException = null)
            : base(message, "GRAPH_API_ERROR",
                  statusCode.HasValue && (statusCode == 429 || statusCode >= 500),
                  null, null, innerException)
        {
            StatusCode = statusCode;
        }
    }

    /// <summary>
    /// Exception for failures in database operations
    /// </summary>
    public class DatabaseException : AppointmentsConsumerException
    {
        /// <summary>
        /// Creates a new instance of DatabaseException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="tenantId">Tenant identifier</param>
        /// <param name="userId">User identifier</param>
        /// <param name="isTransient">Whether the error is transient and can be retried</param>
        /// <param name="innerException">Inner exception</param>
        public DatabaseException(
            string message,
            string? tenantId = null,
            string? userId = null,
            bool isTransient = true,
            Exception? innerException = null)
            : base(message, "DATABASE_ERROR", isTransient, tenantId, userId, innerException)
        {
        }
    }

    /// <summary>
    /// Exception for failures in message deserialization
    /// </summary>
    public class DeserializationException : AppointmentsConsumerException
    {
        /// <summary>
        /// Creates a new instance of DeserializationException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        public DeserializationException(string message, Exception? innerException = null)
            : base(message, "DESERIALIZATION_ERROR", false, null, null, innerException)
        {
        }
    }

    /// <summary>
    /// Exception for token management failures
    /// </summary>
    public class TokenManagementException : AppointmentsConsumerException
    {
        /// <summary>
        /// Creates a new instance of TokenManagementException
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="tenantId">Tenant identifier</param>
        /// <param name="userId">User identifier</param>
        /// <param name="isTransient">Whether the error is transient and can be retried</param>
        /// <param name="innerException">Inner exception</param>
        public TokenManagementException(
            string message,
            string? tenantId = null,
            string? userId = null,
            bool isTransient = true,
            Exception? innerException = null)
            : base(message, "TOKEN_ERROR", isTransient, tenantId, userId, innerException)
        {
        }
    }    /// <summary>
    /// Exception thrown when a token has expired or is invalid
    /// </summary>
    public class TokenExpiredException : AppointmentsConsumerException
    {
        public TokenExpiredException(string message)
            : base(message, "TOKEN_EXPIRED", true)
        {
        }
    }

    /// <summary>
    /// Exception thrown when API throttling or rate limiting occurs
    /// </summary>
    public class ThrottlingException : AppointmentsConsumerException
    {
        /// <summary>
        /// Retry after seconds for throttling
        /// </summary>
        public int RetryAfterSeconds { get; }

        public ThrottlingException(string message)
            : base(message, "THROTTLING", true)
        {
            RetryAfterSeconds = 60; // Default retry after 60 seconds
        }

        public ThrottlingException(string message, int retryAfterSeconds)
            : base(message, "THROTTLING", true)
        {
            RetryAfterSeconds = retryAfterSeconds;
        }
    }

    /// <summary>
    /// Exception thrown when permission issues occur
    /// </summary>
    public class PermissionException : AppointmentsConsumerException
    {
        public PermissionException(string message)
            : base(message, "PERMISSION_DENIED", false)
        {
        }
    }

    /// <summary>
    /// Exception thrown when a resource is not found
    /// </summary>
    public class ResourceNotFoundException : AppointmentsConsumerException
    {
        public ResourceNotFoundException(string message)
            : base(message, "RESOURCE_NOT_FOUND", false)
        {
        }
    }

    /// <summary>
    /// Exception thrown when token acquisition fails
    /// </summary>
    public class TokenAcquisitionException : AppointmentsConsumerException
    {
        public TokenAcquisitionException(string message, Exception? innerException = null, bool isTransient = true)
            : base(message, "TOKEN_ACQUISITION_ERROR", isTransient, null, null, innerException)
        {
        }

        public TokenAcquisitionException(string message, string? tenantId, string? userId, Exception? innerException = null, bool isTransient = true)
            : base(message, "TOKEN_ACQUISITION_ERROR", isTransient, tenantId, userId, innerException)
        {
        }
    }
}
