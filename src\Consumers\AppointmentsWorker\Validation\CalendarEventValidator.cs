using System;
using FluentValidation;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Validation
{
    /// <summary>
    /// Validator for CalendarEvent objects
    /// </summary>
    public class CalendarEventValidator : AbstractValidator<CalendarEvent>
    {
        public CalendarEventValidator()
        {
            // Basic property validations
            RuleFor(x => x.EventId)
                .NotEmpty().WithMessage("EventId is required");

            RuleFor(x => x.TenantId)
                .GreaterThan(0).WithMessage("TenantId must be greater than 0");

            RuleFor(x => x.UserId)
                .GreaterThan(0).WithMessage("UserId must be greater than 0");

            RuleFor(x => x.Title)
                .NotEmpty().WithMessage("Title is required")
                .MaximumLength(255).WithMessage("Title cannot exceed 255 characters");

            // Date validations
            RuleFor(x => x.StartTime)
                .NotNull().WithMessage("StartTime is required");

            RuleFor(x => x.EndTime)
                .NotNull().WithMessage("EndTime is required")
                .GreaterThan(x => x.StartTime).WithMessage("EndTime must be after StartTime");

            // Validate when attendees are present
            When(x => x.Attendees != null && x.Attendees.Count > 0, () =>
            {
                RuleForEach(x => x.Attendees)
                    .SetValidator(new EventAttendeeValidator());
            });

            // Validate location (if present)
            When(x => !string.IsNullOrEmpty(x.Location), () =>
            {
                RuleFor(x => x.Location)
                    .MaximumLength(500).WithMessage("Location cannot exceed 500 characters");
            });

            // Validation for meeting links (if present)
            When(x => !string.IsNullOrEmpty(x.MeetingLink), () =>
            {
                RuleFor(x => x.MeetingLink)
                    .Must(BeValidUrl).WithMessage("Meeting link must be a valid URL");
            });
        }

        private bool BeValidUrl(string? url)
        {
            if (string.IsNullOrEmpty(url))
                return true;

            return Uri.TryCreate(url, UriKind.Absolute, out _);
        }
    }

    /// <summary>
    /// Validator for EventAttendee objects
    /// </summary>
    public class EventAttendeeValidator : AbstractValidator<EventAttendee>
    {
        public EventAttendeeValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("Attendee email is required")
                .EmailAddress().WithMessage("Attendee email must be a valid email address");

            RuleFor(x => x.DisplayName)
                .MaximumLength(255).WithMessage("Attendee name cannot exceed 255 characters");

            RuleFor(x => x.Role)
                .IsInEnum().WithMessage("Attendee role must be a valid role");
        }
    }
}
