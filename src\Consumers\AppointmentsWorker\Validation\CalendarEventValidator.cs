using System;
using FluentValidation;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Validation
{
    /// <summary>
    /// Validator for CalendarEventWrapper objects
    /// </summary>
    public class CalendarEventValidator : AbstractValidator<CalendarEventWrapper>
    {
        public CalendarEventValidator()
        {
            // Basic property validations
            RuleFor(x => x.TenantId)
                .GreaterThan(0).WithMessage("TenantId must be greater than 0");

            RuleFor(x => x.UserId)
                .GreaterThan(0).WithMessage("UserId must be greater than 0");

            RuleFor(x => x.GraphEvent)
                .NotNull().WithMessage("GraphEvent is required");

            // Validate Graph Event properties
            When(x => x.GraphEvent != null, () =>
            {
                RuleFor(x => x.GraphEvent.Subject)
                    .NotEmpty().WithMessage("Subject is required")
                    .MaximumLength(255).WithMessage("Subject cannot exceed 255 characters");

                RuleFor(x => x.GraphEvent.Start)
                    .NotNull().WithMessage("Start time is required");

                RuleFor(x => x.GraphEvent.End)
                    .NotNull().WithMessage("End time is required");

                // Validate location (if present)
                When(x => x.GraphEvent.Location != null && !string.IsNullOrEmpty(x.GraphEvent.Location.DisplayName), () =>
                {
                    RuleFor(x => x.GraphEvent.Location.DisplayName)
                        .MaximumLength(500).WithMessage("Location cannot exceed 500 characters");
                });
            });
        }

        private bool BeValidUrl(string? url)
        {
            if (string.IsNullOrEmpty(url))
                return true;

            return Uri.TryCreate(url, UriKind.Absolute, out _);
        }
    }


}
