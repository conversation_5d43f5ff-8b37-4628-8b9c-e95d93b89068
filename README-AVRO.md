# Avro Deserialization Implementation

This PR introduces strongly-typed Avro deserialization using Confluent's ISpecificRecord interface for improved type safety, performance, and maintainability.

## Changes

1. Added ISpecificRecord implementations for message models:
   - `AppointmentMessage` now implements ISpecificRecord
   - `Message<PERSON>ey` now implements ISpecificRecord
   - Added schema definitions directly in code

2. Enhanced KafkaConsumerFactory:
   - Added `CreateSpecificAvroConsumer<TKey, TValue>()` method for strongly-typed Avro messages
   - Refactored Schema Registry client creation
   - Added support for secure connections to Schema Registry
   
3. Enhanced AvroMessageDeserializer:
   - Simplified implementation to use the ISpecificRecord approach
   - Removed schema caching (handled by CachedSchemaRegistryClient)
   
4. Updated KafkaOptions:
   - Added properties for secure Schema Registry connections
   - Added schema registry authentication options

5. Added Schemas folder:
   - Added Avro schema definitions (.avsc) for models
   - Can be used to generate classes with avrogen if needed in the future

## Benefits

1. **Type Safety**: Avro schema validation at compile time
2. **Performance**: More efficient serialization/deserialization
3. **Schema Evolution**: Better support for schema changes
4. **Maintainability**: Clear mapping between Avro schemas and C# classes

## Next Steps

Consider using avrogen to generate ISpecificRecord implementations directly from schema files for better maintainability.

```bash
dotnet tool install --global Apache.Avro.Tools
avrogen -s schema.avsc output-directory
```
