using System.ComponentModel.DataAnnotations;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration
{
    public class ApplicationOptions
    {
        [Required]
        public string ServiceName { get; set; } = "AppointmentsWorker";
        
        [Range(1, 20)]
        public int ConsumerCount { get; set; } = 3;
        
        [Range(1, 300)]
        public int ProcessingInterval { get; set; } = 5;
        
        [Range(1, 1000)]
        public int? BatchSize { get; set; } = 20;
        
        [Range(100, 100000)]
        public int MaxQueueSize { get; set; } = 10000;
        
        [Range(1, 60)]
        public int? BatchTimeoutSeconds { get; set; } = 5;
        
        [Range(1, 50)]
        public int MaxConcurrentBatches { get; set; } = 10;
        
        public bool EnableUserBatching { get; set; } = true;
        
        [Range(1, 100)]
        public int? UserBatchSize { get; set; } = 20;
        
        [Required]
        public GraphApiOptions GraphApi { get; set; } = new GraphApiOptions();
        
        public string? TokenDecryptionKey { get; set; }
        
        [Range(0, 10)]
        public int MaxRetries { get; set; } = 3;
        
        [Range(100, 10000)]
        public int RetryBaseDelayMs { get; set; } = 500;
        
        public bool EnableDeadLetterQueue { get; set; } = true;
        
        public string DeadLetterTopic { get; set; } = "appointments-dead-letter";
    }
    
    public class GraphApiOptions
    {
        [Required]
        public string BaseUrl { get; set; } = "https://graph.microsoft.com/v1.0/";
        
        [Range(5, 300)]
        public int TimeoutSeconds { get; set; } = 30;
        
        [Range(1, 20)]
        public int MaxBatchSize { get; set; } = 20;
        
        [Range(0, 10)]
        public int RetryCount { get; set; } = 3;
        
        public string[] Scopes { get; set; } = new[] { "https://graph.microsoft.com/.default" };
    }
}
