using System.ComponentModel.DataAnnotations;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration
{
    public class KafkaOptions
    {
        [Required]
        public string BootstrapServers { get; set; } = "localhost:9092";
        
        [Required]
        public string ConsumerGroupId { get; set; } = "appointments-consumer";
        
        [Required]
        public string TopicName { get; set; } = "calendar-events";
        
        [Required]
        public string SchemaRegistryUrl { get; set; } = "http://localhost:8081";
        
        public bool AutoCommitEnabled { get; set; } = false;
        
        [Range(10000, 1800000)]
        public int MaxPollIntervalMs { get; set; } = 300000;
        
        [Range(3000, 300000)]
        public int SessionTimeoutMs { get; set; } = 30000;
        
        [Range(1000, 30000)]
        public int HeartbeatIntervalMs { get; set; } = 3000;
        
        public int FetchMaxBytes { get; set; } = 52428800;
        
        public int MaxPartitionFetchBytes { get; set; } = 1048576;
        
        [Range(1000, 60000)]
        public int AutoCommitIntervalMs { get; set; } = 5000;
        
        public string? SaslUsername { get; set; }
        
        public string? SaslPassword { get; set; }
        
        public string AutoOffsetReset { get; set; } = "earliest";
        
        public bool UseSecureConnection { get; set; } = false;
        
        public string? SchemaRegistryUsername { get; set; }
        
        public string? SchemaRegistryPassword { get; set; }
    }
}
