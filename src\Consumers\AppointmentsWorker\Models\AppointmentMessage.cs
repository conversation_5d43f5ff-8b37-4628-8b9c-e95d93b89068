using System;
using System.Collections.Generic;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models
{
    public class AppointmentMessage
    {
        public long? Version { get; set; }
        public string? Operation { get; set; }
        public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
        public string? CalendarEventId { get; set; }
        public string MessageId { get; set; } = Guid.NewGuid().ToString();
        public long Timestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        public string? Body { get; set; }
        public string? BodyType { get; set; }
        public List<string>? Attendees { get; set; }
        public bool IsAllDay { get; set; }
        public string? Importance { get; set; }
        public string? Sensitivity { get; set; }
        public bool IsDelete => Operation?.ToLower() == "d";
        public string? Id { get; set; }
        public int? Columns { get; set; }
        public int? FirmId { get; set; }
        public string TenantId => FirmId?.ToString() ?? "0";
        public string UserId => Data?.UserId?.ToString() ?? "0";
        public string AppointmentId => Data?.Appo_Id?.ToString() ?? "0";
        public string Subject => Data?.Appo_Subject ?? "No Subject";
        public string Description => Data?.Appo_Description ?? string.Empty;
        public string Location => Data?.Appo_Location ?? string.Empty;
        public AppointmentData? Data { get; set; }
        public string? Database { get; set; }
        public DateTimeOffset StartTime => Data?.Appo_StartTime != null
            ? new DateTimeOffset(Data.Appo_StartTime.Value)
            : DateTimeOffset.UtcNow;
        public DateTimeOffset EndTime => Data?.Appo_EndTime != null
            ? new DateTimeOffset(Data.Appo_EndTime.Value)
            : DateTimeOffset.UtcNow.AddHours(1);
        public string? ExternalEventId { get; set; }
        public string Organizer => string.Empty;
        public bool IsRecurring => false;
        public string? RecurrencePattern { get; set; }
        public DateTimeOffset CreatedDate => DateTimeOffset.UtcNow;
        public string CreatedBy => "AppointmentsConsumer";
        public DateTimeOffset? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
        public string? Status { get; set; }

        public OperationType ToOperationType()
        {
            return Operation?.ToLower() switch
            {
                "i" => OperationType.Create,
                "u" => OperationType.Update,
                "d" => OperationType.Delete,
                _ => OperationType.Create
            };
        }

        public string OperationType => ToOperationType().ToString();
    }

    public class AppointmentData
    {
        public int? FirmId { get; set; }
        public int? UserId { get; set; }
        public string? Appo_Subject { get; set; }
        public int? MatterId { get; set; }
        public string? Appo_Description { get; set; }
        public string? Appo_Location { get; set; }
        public int? Appo_ActiveStatusId { get; set; }
        public int? Appo_Id { get; set; }
        public bool? Appo_IsPrivate { get; set; }
        public DateTime? Appo_StartTime { get; set; }
        public DateTime? Appo_EndTime { get; set; }
    }

    public class MessageKey
    {
        public string FirmId { get; set; } = string.Empty;
        public string Id { get; set; } = string.Empty;
    }

    public enum OperationType
    {
        Create,
        Update,
        Delete
    }
}
