using System;
using Microsoft.Extensions.Configuration;

namespace ConfigChecker
{
    class Program
    {
        static void Main(string[] args)
        {
            var environmentName = args.Length > 0 ? args[0] : "Development";
            
            Console.WriteLine($"Checking configuration for environment: {environmentName}");
            
            // Set environment variable
            Environment.SetEnvironmentVariable("DOTNET_ENVIRONMENT", environmentName);
            
            // Build configuration
            var config = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false)
                .AddJsonFile($"appsettings.{environmentName}.json", optional: true)
                .AddEnvironmentVariables()
                .Build();
                
            // Display configuration values
            Console.WriteLine("\nKafka Settings:");
            Console.WriteLine($"BootstrapServers: {config["Kafka:BootstrapServers"]}");
            Console.WriteLine($"SaslUsername: {config["Kafka:SaslUsername"]}");
            Console.WriteLine($"SaslPassword: {config["Kafka:SaslPassword"]}");
            
            Console.WriteLine("\nMicrosoft Graph API Settings:");
            Console.WriteLine($"Login: {config["MicrosoftGraphApi:Login"]}");
            Console.WriteLine($"GraphUrlBase: {config["MicrosoftGraphApi:GraphUrlBase"]}");
            Console.WriteLine($"ClientId: {config["MicrosoftGraphApi:ClientId"]}");
            Console.WriteLine($"TenantId: {config["MicrosoftGraphApi:TenantId"]}");
            Console.WriteLine($"RedirectUrl: {config["MicrosoftGraphApi:RedirectUrl"]}");
            
            Console.WriteLine("\nDatabase Settings:");
            Console.WriteLine($"SystemConnectionString: {config["Database:SystemConnectionString"]}");
        }
    }
}
