using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.CircuitBreaker;
using Polly.Contrib.WaitAndRetry;
using Polly.Registry;
using Polly.Timeout;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience
{
    /// <summary>
    /// Provides resilience policies for the appointment consumer service
    /// </summary>
    public class ResiliencePolicies
    {
        private readonly ILogger<ResiliencePolicies> _logger;
        private readonly ApplicationOptions _options;
        private readonly PolicyRegistry _policyRegistry;

        // Circuit breaker instance for Graph API calls
        private readonly AsyncCircuitBreakerPolicy _graphCircuitBreaker;

        /// <summary>
        /// Policy registry containing named policies
        /// </summary>
        public IPolicyRegistry<string> Registry => _policyRegistry;

        /// <summary>
        /// Creates a new instance of ResiliencePolicies
        /// </summary>
        public ResiliencePolicies(ILogger<ResiliencePolicies> logger, IOptions<ApplicationOptions> options)
        {
            _logger = logger;
            _options = options.Value;
            _policyRegistry = new PolicyRegistry();

            // Create circuit breaker for Graph API calls
            _graphCircuitBreaker = Policy
                .Handle<GraphApiException>(ex => ex.IsTransient)
                .Or<TimeoutRejectedException>()
                .Or<HttpRequestException>()
                .AdvancedCircuitBreakerAsync(
                    failureThreshold: 0.5, // 50% failure threshold
                    samplingDuration: TimeSpan.FromMinutes(2),
                    minimumThroughput: 10,
                    durationOfBreak: TimeSpan.FromMinutes(1),
                    onBreak: (ex, timespan, context) =>
                    {
                        _logger.LogWarning(ex,
                            "Circuit breaker for {PolicyKey} opened. Duration: {BreakDuration}",
                            context?.PolicyKey ?? "Unknown",
                            timespan);
                    },
                    onReset: context =>
                    {
                        _logger.LogInformation(
                            "Circuit breaker for {PolicyKey} reset",
                            context?.PolicyKey ?? "Unknown");
                    },
                    onHalfOpen: () =>
                    {
                        _logger.LogInformation("Circuit breaker is now half-open");
                    });

            // Register all policies
            RegisterGraphApiPolicies();
            RegisterDatabasePolicies();
            RegisterConsumerPolicies();
        }

        /// <summary>
        /// Registers policies for Graph API operations
        /// </summary>
        private void RegisterGraphApiPolicies()
        {
            // Define retry strategy
            var graphRetryDelay = Backoff.DecorrelatedJitterBackoffV2(
                medianFirstRetryDelay: TimeSpan.FromSeconds(1),
                retryCount: _options.GraphApi.RetryCount,
                fastFirst: true);

            // Create retry policy
            var graphRetryPolicy = Policy
                .Handle<GraphApiException>(ex => ex.IsTransient)
                .Or<HttpRequestException>()
                .Or<TimeoutRejectedException>()
                .WaitAndRetryAsync(
                    graphRetryDelay,
                    onRetry: (outcome, delay, retryCount, context) =>
                    {
                        _logger.LogWarning(outcome.Exception,
                            "Retry {RetryCount} for Graph API call after {Delay}ms. Context: {PolicyKey}",
                            retryCount,
                            delay.TotalMilliseconds,
                            context?.PolicyKey ?? "Unknown");
                    });

            // Create timeout policy
            var graphTimeoutPolicy = Policy
                .TimeoutAsync(
                    timeout: TimeSpan.FromSeconds(_options.GraphApi.TimeoutSeconds),
                    timeoutStrategy: TimeoutStrategy.Pessimistic,
                    onTimeoutAsync: (context, timespan, task, token) =>
                    {
                        _logger.LogWarning(
                            "Graph API call timed out after {Timeout}ms. Context: {PolicyKey}",
                            timespan.TotalMilliseconds,
                            context?.PolicyKey ?? "Unknown");
                        return Task.CompletedTask;
                    });

            // Create combined policy
            var graphPolicy = Policy.WrapAsync(graphRetryPolicy, _graphCircuitBreaker, graphTimeoutPolicy);

            // Register in registry
            _policyRegistry.Add("GraphApiPolicy", graphPolicy);
        }

        /// <summary>
        /// Registers policies for database operations
        /// </summary>
        private void RegisterDatabasePolicies()
        {
            // Define retry strategy
            var dbRetryDelay = Backoff.ExponentialBackoff(
                TimeSpan.FromMilliseconds(_options.RetryBaseDelayMs),
                retryCount: _options.MaxRetries);

            // Create retry policy
            var dbRetryPolicy = Policy
                .Handle<DatabaseException>(ex => ex.IsTransient)
                .WaitAndRetryAsync(
                    dbRetryDelay,
                    onRetry: (outcome, delay, retryCount, context) =>
                    {
                        _logger.LogWarning(outcome.Exception,
                            "Retry {RetryCount} for database operation after {Delay}ms. Context: {PolicyKey}",
                            retryCount,
                            delay.TotalMilliseconds,
                            context?.PolicyKey ?? "Unknown");
                    });

            // Create timeout policy
            var dbTimeoutPolicy = Policy
                .TimeoutAsync(
                    timeout: TimeSpan.FromSeconds(30),
                    timeoutStrategy: TimeoutStrategy.Pessimistic,
                    onTimeoutAsync: (context, timespan, task, token) =>
                    {
                        _logger.LogWarning(
                            "Database operation timed out after {Timeout}ms. Context: {PolicyKey}",
                            timespan.TotalMilliseconds,
                            context?.PolicyKey ?? "Unknown");
                        return Task.CompletedTask;
                    });

            // Create combined policy
            var dbPolicy = Policy.WrapAsync(dbRetryPolicy, dbTimeoutPolicy);

            // Register in registry
            _policyRegistry.Add("DatabasePolicy", dbPolicy);
        }

        /// <summary>
        /// Registers policies for Kafka consumer operations
        /// </summary>
        private void RegisterConsumerPolicies()
        {
            // Define retry strategy for consumer connection
            var consumerRetryDelay = Backoff.DecorrelatedJitterBackoffV2(
                medianFirstRetryDelay: TimeSpan.FromSeconds(1),
                retryCount: 5,
                fastFirst: true);

            // Create retry policy
            var consumerRetryPolicy = Policy
                .Handle<Exception>(ex => !(ex is OperationCanceledException))
                .WaitAndRetryAsync(
                    consumerRetryDelay,
                    onRetry: (outcome, delay, retryCount, context) =>
                    {
                        _logger.LogWarning(outcome.Exception,
                            "Retry {RetryCount} for consumer operation after {Delay}ms. Context: {PolicyKey}",
                            retryCount,
                            delay.TotalMilliseconds,
                            context?.PolicyKey ?? "Unknown");
                    });

            // Register in registry
            _policyRegistry.Add("ConsumerPolicy", consumerRetryPolicy);
        }

        /// <summary>
        /// Gets the policy for Graph API operations
        /// </summary>
        public IAsyncPolicy GraphApiPolicy => _policyRegistry.Get<IAsyncPolicy>("GraphApiPolicy");

        /// <summary>
        /// Gets the policy for database operations
        /// </summary>
        public IAsyncPolicy DatabasePolicy => _policyRegistry.Get<IAsyncPolicy>("DatabasePolicy");

        /// <summary>
        /// Gets the policy for consumer operations
        /// </summary>
        public IAsyncPolicy ConsumerPolicy => _policyRegistry.Get<IAsyncPolicy>("ConsumerPolicy");

        /// <summary>
        /// Creates a resilience policy for Graph API operations
        /// </summary>
        /// <param name="logger">Logger for policy actions</param>
        /// <returns>An async policy for Graph API calls</returns>
        public static IAsyncPolicy CreateGraphApiPolicy(ILogger logger)
        {
            // Define retry delays with exponential backoff
            var delay = Backoff.ExponentialBackoff(
                TimeSpan.FromSeconds(1),
                retryCount: 3,
                factor: 2);

            // Create retry policy for transient errors
            var retryPolicy = Policy
                .Handle<HttpRequestException>()
                .Or<TimeoutRejectedException>()
                .Or<BrokenCircuitException>()
                .Or<GraphApiException>(ex => ex.IsTransient)
                .WaitAndRetryAsync(
                    delay,
                    (exception, timeSpan, retryCount, context) =>
                    {
                        logger.LogWarning(
                            exception,
                            "Error in Graph API call (attempt {RetryCount}). Waiting {RetryDelay}ms before retry.",
                            retryCount,
                            timeSpan.TotalMilliseconds);
                    });

            // Create circuit breaker policy
            var circuitBreakerPolicy = Policy
                .Handle<HttpRequestException>()
                .Or<TimeoutRejectedException>()
                .Or<GraphApiException>(ex => ex.IsTransient)
                .CircuitBreakerAsync(
                    exceptionsAllowedBeforeBreaking: 5,
                    durationOfBreak: TimeSpan.FromMinutes(1),
                    onBreak: (ex, breakDelay) =>
                    {
                        logger.LogError(
                            ex,
                            "Circuit breaker opened for {BreakDelay}ms due to Graph API errors.",
                            breakDelay.TotalMilliseconds);
                    },
                    onReset: () =>
                    {
                        logger.LogInformation("Circuit breaker reset. Graph API operations resume normal execution.");
                    },
                    onHalfOpen: () =>
                    {
                        logger.LogInformation("Circuit breaker half-open. Testing if Graph API is responsive.");
                    });

            // Create timeout policy
            var timeoutPolicy = Policy.TimeoutAsync(
                TimeSpan.FromSeconds(30),
                TimeoutStrategy.Pessimistic,
                onTimeoutAsync: (context, timeSpan, task) =>
                {
                    logger.LogWarning(
                        "Graph API operation timed out after {Timeout}ms.",
                        timeSpan.TotalMilliseconds);

                    return Task.CompletedTask;
                });

            // Combine policies
            return Policy.WrapAsync(retryPolicy, circuitBreakerPolicy, timeoutPolicy);
        }

        /// <summary>
        /// Creates a resilience policy for token acquisition operations
        /// </summary>
        /// <param name="logger">Logger for policy actions</param>
        /// <returns>An async policy for token acquisition calls</returns>
        public static AsyncPolicy CreateTokenAcquisitionPolicy(ILogger logger)
        {
            // Define retry delays with exponential backoff
            var delay = Backoff.ExponentialBackoff(
                TimeSpan.FromSeconds(1),
                retryCount: 3,
                factor: 2);

            // Create retry policy for transient errors
            var retryPolicy = Policy
                .Handle<TokenAcquisitionException>(ex => ex.IsTransient)
                .Or<HttpRequestException>()
                .Or<TimeoutRejectedException>()
                .WaitAndRetryAsync(
                    delay,
                    (exception, timeSpan, retryCount, context) =>
                    {
                        logger.LogWarning(
                            exception,
                            "Error in token acquisition (attempt {RetryCount}). Waiting {RetryDelay}ms before retry.",
                            retryCount,
                            timeSpan.TotalMilliseconds);
                    });

            // Create timeout policy
            var timeoutPolicy = Policy.TimeoutAsync(
                TimeSpan.FromSeconds(30),
                TimeoutStrategy.Pessimistic,
                onTimeoutAsync: (context, timeSpan, task) =>
                {
                    logger.LogWarning(
                        "Token acquisition operation timed out after {Timeout}ms.",
                        timeSpan.TotalMilliseconds);

                    return Task.CompletedTask;
                });

            // Combine policies
            return Policy.WrapAsync(retryPolicy, timeoutPolicy);
        }
    }
}
