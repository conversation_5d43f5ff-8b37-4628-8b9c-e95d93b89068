using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Serialization
{
    /// <summary>
    /// Helper class for JSON serialization and deserialization operations using Newtonsoft.Json.
    /// </summary>
    public static class Json<PERSON>elper
    {
        private static readonly JsonSerializerSettings DefaultSettings = new JsonSerializerSettings
        {
            NullValueHandling = NullValueHandling.Ignore,
            DateFormatHandling = DateFormatHandling.IsoDateFormat,
            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore
        };

        /// <summary>
        /// Serializes an object to a JSON string.
        /// </summary>
        /// <typeparam name="T">Type of object to serialize</typeparam>
        /// <param name="value">Object to serialize</param>
        /// <param name="settings">Optional serializer settings</param>
        /// <returns>JSON string representation of the object</returns>
        public static string Serialize<T>(T value, JsonSerializerSettings? settings = null)
        {
            return JsonConvert.SerializeObject(value, settings ?? DefaultSettings);
        }

        /// <summary>
        /// Deserializes a JSON string to an object.
        /// </summary>
        /// <typeparam name="T">Type to deserialize to</typeparam>
        /// <param name="json">JSON string to deserialize</param>
        /// <param name="settings">Optional serializer settings</param>
        /// <returns>Deserialized object</returns>
        public static T? Deserialize<T>(string json, JsonSerializerSettings? settings = null)
        {
            return JsonConvert.DeserializeObject<T>(json, settings ?? DefaultSettings);
        }

        /// <summary>
        /// Creates a StringContent with JSON media type from an object.
        /// </summary>
        /// <typeparam name="T">Type of object to serialize</typeparam>
        /// <param name="value">Object to serialize</param>
        /// <returns>StringContent with JSON content type</returns>
        public static System.Net.Http.StringContent ToJsonContent<T>(T value)
        {
            var json = Serialize(value);
            return new System.Net.Http.StringContent(json, Encoding.UTF8, "application/json");
        }
    }
}
