# Worker and Program Implementation

## 1. Worker Implementation

### Worker.cs

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Confluent.Kafka;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Serilog;
using WorkerService.Models;
using WorkerService.Services;

namespace WorkerService;

public class Worker : BackgroundService
{
    private readonly ConsumerService _consumer;
    private readonly CalendarSyncService _syncService;
    private readonly CalendarSyncOptions _options;

    public Worker(
        ConsumerService consumer,
        CalendarSyncService syncService,
        IOptionsMonitor<CalendarSyncOptions> options)
    {
        _consumer = consumer;
        _syncService = syncService;
        _options = options.CurrentValue;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        Log.Information("Calendar Sync Service started at: {time}", DateTimeOffset.Now);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var cancellation = CancellationTokenSource.CreateLinkedTokenSource(
                    stoppingToken,
                    new CancellationTokenSource(TimeSpan.FromMinutes(15)).Token).Token;

                var tasks = new List<Task>();

                // Define topics to consume
                var topics = new[] { "calendar-events" };

                foreach (var topic in topics)
                {
                    tasks.Add(Task.Run(async () =>
                    {
                        await _consumer.ConsumeAsync(
                            ProcessEventsWithBatchingAsync,
                            topic,
                            "calendar-sync-group",
                            cancellation);
                    }, stoppingToken));
                }

                await Task.WhenAll(tasks);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in worker execution");
                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
            }
        }

        Log.Information("Calendar Sync Service has stopped.");
    }

    private async ValueTask ProcessAppointmentMessagesWithBatchingAsync(
        IAsyncEnumerable<ConsumeResult<MessageKey, AppointmentMessage>> events,
        CancellationToken token)
    {
        var batch = new List<CalendarEvent>();

        await foreach (var result in events.WithCancellation(token))
        {
            try
            {
                var message = result.Message.Value;
                
                if (message?.Data == null || !message.Data.UserId.HasValue || !message.Data.Appo_Id.HasValue)
                {
                    Log.Warning("Received invalid appointment message: {MessageId}", result.Message.Key);
                    continue;
                }

                // Convert appointment message to calendar event
                var calendarEvent = new CalendarEvent
                {
                    EventId = Guid.NewGuid(),
                    TenantId = message.Data.FirmId ?? 0,
                    UserId = message.Data.UserId.Value,
                    Title = message.Data.Appo_Subject ?? "No Title",
                    Description = message.Data.Appo_Description,
                    Location = message.Data.Appo_Location,
                    // Set appropriate start/end times from appointment data
                    StartTime = DateTime.UtcNow, // This should come from appointment data 
                    EndTime = DateTime.UtcNow.AddHours(1), // This should come from appointment data
                    Operation = message.ToOperation()
                };
                
                batch.Add(calendarEvent);

                // Process batch when it reaches the configured size
                if (batch.Count >= _options.BatchSize)
                {
                    try
                    {
                        await _syncService.ProcessBatchAsync(batch, token);
                    }
                    catch (Microsoft.Identity.Client.MsalUiRequiredException ex)
                    {
                        // This is handled in the CalendarSyncService by invalidating the token
                        Log.Warning(ex, "Authentication error occurred while processing batch. Token will be invalidated.");
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error processing batch of {Count} events", batch.Count);
                    }
                    finally
                    {
                        batch.Clear();
                    }
                }
                
                // Store offset
                _consumer.StoreOffset(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing appointment message");
                // Continue processing other events
            }
        }

        // Process any remaining events
        if (batch.Count > 0)
        {
            try
            {
                await _syncService.ProcessBatchAsync(batch, token);
            }
            catch (Microsoft.Identity.Client.MsalUiRequiredException ex)
            {
                // This is handled in the CalendarSyncService by invalidating the token
                Log.Warning(ex, "Authentication error occurred while processing final batch. Token will be invalidated.");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing final batch of {Count} events", batch.Count);
            }
        }
    }
}
```

## 2. Program Setup

### Program.cs

```csharp
using System;
using System.IO;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Events;
using WorkerService.Contexts;
using WorkerService.Factories;
using WorkerService.Gateways;
using WorkerService.Services;

namespace WorkerService;

public class Program
{
    public static async Task Main(string[] args)
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .CreateLogger();

        try
        {
            Log.Information("Starting Calendar Sync Service");
            await CreateHostBuilder(args).Build().RunAsync();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Calendar Sync Service terminated unexpectedly");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureAppConfiguration((hostContext, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .AddJsonFile($"appsettings.{hostContext.HostingEnvironment.EnvironmentName}.json", optional: true, reloadOnChange: true)
                    .AddEnvironmentVariables()
                    .AddCommandLine(args);
            })
            .ConfigureServices((context, services) =>
            {
                // Configure options
                ConfigureOptions<CalendarSyncOptions>(services);
                ConfigureOptions<KafkaOptions>(services);
                ConfigureOptions<SystemDbOptions>(services);

                // Add database services
                services.AddMemoryCache();
                services.AddDbContextFactory<SystemDbContext>((provider, dbContext) =>
                {
                    var options = provider.GetRequiredService<IOptionsMonitor<SystemDbOptions>>().CurrentValue;
                    dbContext.UseSqlServer(options.ConnectionString, sqlServer =>
                        sqlServer.EnableRetryOnFailure(
                            maxRetryCount: options.MaxRetryCount,
                            maxRetryDelay: options.MaxRetryDelay,
                            errorNumbersToAdd: options.ErrorNumbersToAdd));
                });

                // Add schema registry
                services.AddSingleton<ISchemaRegistryClient>(provider =>
                {
                    var kafkaOptions = provider.GetRequiredService<IOptionsMonitor<KafkaOptions>>().CurrentValue;
                    return new CachedSchemaRegistryClient(new SchemaRegistryConfig
                    {
                        Url = context.Configuration.GetConnectionString("SchemaRegistry")
                    });
                });
                
                // Add Avro serializers for message key and appointment message
                services.AddSingleton<IDeserializer<MessageKey>>(provider => 
                    new AvroDeserializer<MessageKey>(provider.GetRequiredService<ISchemaRegistryClient>()));
                    
                services.AddSingleton<IDeserializer<AppointmentMessage>>(provider => 
                    new AvroDeserializer<AppointmentMessage>(provider.GetRequiredService<ISchemaRegistryClient>()));

                // Register HTTP client for Graph API calls
                services.AddHttpClient("GraphApi", client =>
                {
                    client.BaseAddress = new Uri("https://graph.microsoft.com/v1.0/");
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                });

                // Add services
                services.AddSingleton<TenantResolver>();
                services.AddSingleton<SqlGateway>();
                services.AddSingleton<TokenService>();
                services.AddSingleton<GraphService>();
                services.AddSingleton<ConsumerFactory<MessageKey, AppointmentMessage>>();
                services.AddSingleton<ConsumerService>();
                services.AddSingleton<CalendarSyncService>();

                // Add hosted service
                services.AddHostedService<Worker>();
            });

    private static void ConfigureOptions<TOptions>(IServiceCollection services) where TOptions : class =>
        services
            .AddOptionsWithValidateOnStart<TOptions>()
            .BindConfiguration(typeof(TOptions).Name)
            .ValidateDataAnnotations();
}
```

## 3. Configuration Files

### appsettings.json

```json
{
  "ConnectionStrings": {
    "SchemaRegistry": "http://localhost:8081"
  },
  "CalendarSyncOptions": {
    "BatchSize": 100,
    "Concurrency": 4,
    "ChannelCapacity": 10000,
    "CacheExpirationMin": 15,
    "ConsumeTimeoutMs": 10000,
    "MaxBackoffDelayMs": 5000
  },
  "SystemDbOptions": {
    "ConnectionString": "Server=localhost;Database=CalendarSync;Trusted_Connection=True;TrustServerCertificate=True;",
    "MaxRetryCount": 5,
    "MaxRetryDelay": "00:00:05",
    "ErrorNumbersToAdd": []
  },
  "KafkaOptions": {
    "BootstrapServers": "localhost:9092",
    "AutoOffsetReset": 1,
    "FetchWaitMaxMs": 100,
    "MaxPollIntervalMs": 1200000,
    "AutoCommitIntervalMs": 5000
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "logs/calendar-sync-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 7
        }
      }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ]
  }
}
```

### appsettings.Development.json

```json
{
  "ConnectionStrings": {
    "SchemaRegistry": "http://localhost:8081"
  },
  "CalendarSyncOptions": {
    "BatchSize": 10,
    "Concurrency": 2
  },
  "KafkaOptions": {
    "BootstrapServers": "localhost:9092"
  },
  "SystemDbOptions": {
    "ConnectionString": "Server=localhost;Database=CalendarSync;Trusted_Connection=True;TrustServerCertificate=True;"
  }
}
```

## 4. Tenant Resolver Implementation

### TenantResolver.cs

```csharp
using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Serilog;

namespace WorkerService.Contexts;

public class TenantResolver
{
    private readonly IDbContextFactory<SystemDbContext> _factory;
    private readonly IMemoryCache _cache;
    private readonly CalendarSyncOptions _options;

    public TenantResolver(
        IDbContextFactory<SystemDbContext> factory,
        IMemoryCache cache,
        IOptionsMonitor<CalendarSyncOptions> options)
    {
        _factory = factory;
        _cache = cache;
        _options = options.CurrentValue;
    }

    public async ValueTask<SqlConnection?> GetTenantConnectionAsync(string tenantId, CancellationToken token)
    {
        var cacheKey = $"tenant:{tenantId}";

        var connectionString = await _cache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_options.CacheExpirationMin);
            return await GetConnectionStringFromDbAsync(tenantId, token);
        });

        return connectionString is null ? null : new SqlConnection(connectionString);
    }

    private async Task<string?> GetConnectionStringFromDbAsync(string tenantId, CancellationToken token)
    {
        try
        {
            await using var context = await _factory.CreateDbContextAsync(token);
            var tenant = await context.Tenants
                .AsNoTracking()
                .FirstOrDefaultAsync(t => t.TenantId.ToString() == tenantId, token);

            return tenant?.ConnectionString;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error retrieving connection string for tenant {TenantId}", tenantId);
            return null;
        }
    }
}
```

### SystemDbContext.cs

```csharp
using Microsoft.EntityFrameworkCore;

namespace WorkerService.Contexts;

public class SystemDbContext : DbContext
{
    public SystemDbContext(DbContextOptions<SystemDbContext> options) : base(options)
    {
    }

    public DbSet<Tenant> Tenants { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.HasKey(e => e.TenantId);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ConnectionString).IsRequired().HasMaxLength(500);
        });
    }
}

public class Tenant
{
    public int TenantId { get; set; }
    public string Name { get; set; } = null!;
    public string ConnectionString { get; set; } = null!;
    public bool IsActive { get; set; } = true;
}
```
