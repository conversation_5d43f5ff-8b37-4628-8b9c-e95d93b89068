using System;
using FluentValidation;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Validation
{
    /// <summary>
    /// Validator for UserSyncConfig objects
    /// </summary>
    public class UserSyncConfigValidator : AbstractValidator<UserSyncConfig>
    {
        public UserSyncConfigValidator()
        {
            // Basic property validations
            RuleFor(x => x.UserId)
                .GreaterThan(0).WithMessage("UserId must be greater than 0");

            RuleFor(x => x.TenantId)
                .GreaterThan(0).WithMessage("TenantId must be greater than 0");

            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("Email is required")
                .EmailAddress().WithMessage("Email must be a valid email address");

            // Token validation
            RuleFor(x => x.AccessToken)
                .NotEmpty().When(x => x.IsActive).WithMessage("AccessToken is required when synchronization is active");

            RuleFor(x => x.RefreshToken)
                .NotEmpty().When(x => x.IsActive).WithMessage("RefreshToken is required when synchronization is active");

            // Calendar ID validation (if present)
            When(x => !string.IsNullOrEmpty(x.CalendarId), () =>
            {
                RuleFor(x => x.CalendarId)
                    .MaximumLength(500).WithMessage("CalendarId cannot exceed 500 characters");
            });

            // Validate token expiration
            RuleFor(x => x.TokenExpiresAt)
                .NotNull().When(x => x.IsActive).WithMessage("TokenExpiresAt is required when synchronization is active");

            // Validate last sync time makes sense
            When(x => x.LastSyncTime.HasValue, () =>
            {
                RuleFor(x => x.LastSyncTime)
                    .LessThanOrEqualTo(_ => DateTime.UtcNow).WithMessage("LastSyncTime cannot be in the future");
            });

            // Validate last sync attempt timestamp makes sense
            When(x => x.LastSyncAttemptTime.HasValue, () =>
            {
                RuleFor(x => x.LastSyncAttemptTime)
                    .LessThanOrEqualTo(_ => DateTime.UtcNow).WithMessage("LastSyncAttemptTime cannot be in the future");
            });

            // Validate the sync status makes sense when there's an error
            When(x => !string.IsNullOrEmpty(x.LastSyncError), () =>
            {
                RuleFor(x => x.SyncStatus)
                    .Equal(SyncStatus.Error).WithMessage("SyncStatus should be Error when LastSyncError is present");
            });
        }
    }
}
