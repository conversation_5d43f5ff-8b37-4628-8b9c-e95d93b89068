# Calendar Event Synchronization Project

## Project Overview

Create a distributed, scalable background service designed to synchronize calendar events between different systems with minimal latency and high resilience. The service should:

1. Consume calendar event changes from a message broker (Kafka)
2. Process events in batches for efficiency
3. Perform Create, Update, Delete (CUD) operations in a SQL database
4. Support multiple tenants/firms
5. Be highly scalable and resilient

## Architecture

Follow the architecture pattern from the DynamicIndexer project, which consists of:

### 1. Worker Service

A background service that:
- Consumes messages from Kafka topics
- Processes messages concurrently using channels for backpressure
- Batches operations for efficiency
- Handles database operations

### 2. Core Components

#### MessageBus
- Implements a bounded channel for decoupling Kafka consumption from processing
- Supports backpressure with configurable concurrency
- Handles error resilience and offset management

#### CalendarSyncService
- Processes calendar events
- Performs database operations (Create, Update, Delete)
- Handles batching of operations
- Groups operations by user for batched Graph API calls
- Manages authentication tokens for users
- Updates appointment status after processing

#### SqlGateway
- Manages database connections
- Executes SQL queries
- Supports batched operations

## Implementation Details

### 1. Configuration

```csharp
// CalendarSyncOptions.cs
public record CalendarSyncOptions
{
    [Required] public int BatchSize { get; init; } = 100;
    [Required] public int ChannelCapacity { get; init; } = 10000;
    [Required] public int Concurrency { get; init; } = 4;
    [Required] public int CacheExpirationMin { get; init; } = 15;
    [Required] public int ConsumeTimeoutMs { get; init; } = 10000;
    [Required] public double MaxBackoffDelayMs { get; init; } = 5000;
    [Required] public int CommitIntervalMs { get; init; } = 5000;
}
```

### 2. Message Bus Implementation

```csharp
// MessageBus.cs
public class MessageBus<TKey, TValue>
{
    private readonly Channel<ConsumeResult<TKey, TValue>> _channel;
    private IConsumer<TKey, TValue> _consumer = null!;
    private readonly CalendarSyncOptions _options;
    private readonly ConsumerFactory<TKey, TValue> _factory;

    public MessageBus(IOptionsMonitor<CalendarSyncOptions> options, ConsumerFactory<TKey, TValue> factory)
    {
        _options = options.CurrentValue;
        _factory = factory;
        _channel = Channel.CreateBounded<ConsumeResult<TKey, TValue>>(
            new BoundedChannelOptions(_options.ChannelCapacity)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleWriter = true,
                SingleReader = false
            });
    }

    public IAsyncEnumerable<ConsumeResult<TKey, TValue>> SubscribeAsync(CancellationToken cancellation)
        => _channel.Reader.ReadAllAsync(cancellation);

    public void StoreOffset(ConsumeResult<TKey, TValue> message) => _consumer.StoreOffset(message);

    public async ValueTask StartConsumingAsync(string consumerGroup, string topic, CancellationToken cancellation)
    {
        _consumer = _factory.CreateConsumer(consumerGroup);
        _consumer.Subscribe(topic);

        while (!cancellation.IsCancellationRequested)
        {
            try
            {
                var result = _consumer.Consume(_options.ConsumeTimeoutMs);
                if (result?.Message is null) continue;
                if (result.IsPartitionEOF) continue;
                if (cancellation.IsCancellationRequested) break;

                await ApplyBackpressureAsync(cancellation);
                await _channel.Writer.WriteAsync(result, cancellation);
            }
            catch (Exception ex)
            {
                // Handle exceptions
                Log.Error(ex, "Error consuming message from Kafka");
            }
        }

        _consumer.Close();
        _channel.Writer.Complete();
    }

    private async ValueTask ApplyBackpressureAsync(CancellationToken cancellation)
    {
        var capacity = _options.ChannelCapacity;
        var maxBackoffDelayMs = _options.MaxBackoffDelayMs;
        var currentCount = _channel.Reader.CanCount ? _channel.Reader.Count : 0;

        if (currentCount <= 0) return;

        var ratio = currentCount / (double)capacity;
        var delayMs = (int)(ratio * maxBackoffDelayMs);

        if (delayMs <= 0) return;

        await Task.Delay(delayMs, cancellation);
    }
}
```

### 3. Avro Deserializer Implementation

```csharp
// AvroDeserializer.cs
public class AvroDeserializer<T> : IDeserializer<T> where T : class
{
    private readonly ISchemaRegistryClient _schemaRegistryClient;

    public AvroDeserializer(ISchemaRegistryClient schemaRegistryClient)
    {
        _schemaRegistryClient = schemaRegistryClient;
    }

    public T Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
    {
        if (isNull) return null;

        try
        {
            // The Avro serialized data is preceded by a "magic byte" (0) and the schema id (4 bytes)
            var magicByte = data[0];
            var schemaId = BitConverter.ToInt32(data.Slice(1, 4));
            var schema = _schemaRegistryClient.GetSchemaAsync(schemaId).GetAwaiter().GetResult();

            var reader = new GenericDatumReader<GenericRecord>(Schema.Parse(schema.SchemaString));
            var decoder = new BinaryDecoder(data.Slice(5).ToArray());
            var record = reader.Read(null, decoder);

            if (typeof(T) == typeof(AppointmentMessage))
            {
                return DeserializeAppointmentMessage(record) as T;
            }
            else if (typeof(T) == typeof(MessageKey))
            {
                return DeserializeMessageKey(record) as T;
            }

            return null;
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error deserializing Avro message");
            return null;
        }
    }

    private AppointmentMessage DeserializeAppointmentMessage(GenericRecord record)
    {
        var appointmentMessage = new AppointmentMessage
        {
            Version = GetNullableValue<long>(record, "Version"),
            Operation = GetNullableValue<string>(record, "Operation"),
            Id = GetNullableValue<string>(record, "Id"),
            Columns = GetNullableValue<int>(record, "Columns"),
            FirmId = GetNullableValue<int>(record, "FirmId"),
            Database = GetNullableValue<string>(record, "Database")
        };

        var dataRecord = record.GetField("Data") as GenericRecord;
        if (dataRecord != null)
        {
            appointmentMessage.Data = new AppointmentData
            {
                FirmId = GetNullableValue<int>(dataRecord, "firmId"),
                UserId = GetNullableValue<int>(dataRecord, "userId"),
                Appo_Subject = GetNullableValue<string>(dataRecord, "appo_subject"),
                MatterId = GetNullableValue<int>(dataRecord, "matterId"),
                Appo_Description = GetNullableValue<string>(dataRecord, "appo_description"),
                Appo_Location = GetNullableValue<string>(dataRecord, "appo_location"),
                Appo_ActiveStatusId = GetNullableValue<int>(dataRecord, "appo_activestatusid"),
                Appo_Id = GetNullableValue<int>(dataRecord, "appo_id"),
                Appo_IsPrivate = GetNullableValue<bool>(dataRecord, "appo_IsPrivate")
            };
        }

        return appointmentMessage;
    }

    private MessageKey DeserializeMessageKey(GenericRecord record)
    {
        return new MessageKey
        {
            FirmId = GetValue<string>(record, "FirmId"),
            Id = GetValue<string>(record, "Id")
        };
    }

    // Helper methods for value extraction
    private static T GetValue<T>(GenericRecord record, string fieldName)
    {
        if (record.TryGetValue(fieldName, out var value))
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }
        return default;
    }

    private static T? GetNullableValue<T>(GenericRecord record, string fieldName) where T : struct
    {
        if (record.TryGetValue(fieldName, out var value) && value != null)
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }
        return null;
    }

    private static string GetNullableValue<T>(GenericRecord record, string fieldName) where T : class
    {
        if (record.TryGetValue(fieldName, out var value) && value != null)
        {
            return value.ToString();
        }
        return null;
    }
}
```

### 4. Token Service for User Authentication

```csharp
// TokenService.cs
public class TokenService
{
    private readonly TenantResolver _resolver;
    private readonly IMemoryCache _cache;

    public TokenService(TenantResolver resolver, IMemoryCache cache)
    {
        _resolver = resolver;
        _cache = cache;
    }

    /// <summary>
    /// Gets the access token for a specific user
    /// </summary>
    public async Task<string> GetAccessTokenAsync(int userId, int tenantId, CancellationToken token)
    {
        var cacheKey = $"token:{userId}:{tenantId}";

        return await _cache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10); // Cache for 10 minutes

            try
            {
                var userConfig = await GetUserSyncConfigAsync(userId, tenantId, token);
                
                if (userConfig == null || !userConfig.IsValid)
                {
                    Log.Warning("No valid token configuration for user {UserId} in tenant {TenantId}", userId, tenantId);
                    return null;
                }
                
                // Security column contains access token info as JSON
                if (!string.IsNullOrEmpty(userConfig.Security))
                {
                    try
                    {
                        var tokenInfo = JsonDocument.Parse(userConfig.Security);
                        if (tokenInfo.RootElement.TryGetProperty("access_token", out var accessToken))
                        {
                            return accessToken.GetString();
                        }
                    }
                    catch (JsonException ex)
                    {
                        Log.Error(ex, "Error parsing token data from Security column for user {UserId}", userId);
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error retrieving access token for user {UserId}", userId);
                return null;
            }
        });
    }

    /// <summary>
    /// Marks a user's token as invalid in the database
    /// </summary>
    public async Task InvalidateTokenAsync(int userId, int tenantId, CancellationToken token)
    {
        try
        {
            var connection = await _resolver.GetTenantConnectionAsync(tenantId.ToString(), token);
            if (connection == null) return;

            await connection.OpenAsync(token);

            using var command = connection.CreateCommand();
            command.CommandText = @"
                UPDATE T_UserSyncConfig
                SET IsValid = 0, UpdatedAt = GETUTCDATE()
                WHERE UserId = @UserId AND TenantId = @TenantId";

            command.Parameters.AddWithValue("@UserId", userId);
            command.Parameters.AddWithValue("@TenantId", tenantId);

            await command.ExecuteNonQueryAsync(token);
            _cache.Remove(cacheKey);

            Log.Information("Token invalidated for user {UserId} in tenant {TenantId}", userId, tenantId);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error invalidating token for user {UserId} in tenant {TenantId}", userId, tenantId);
        }
    }

    /// <summary>
    /// Retrieves the user sync configuration from the database
    /// </summary>
    private async Task<UserSyncConfig> GetUserSyncConfigAsync(int userId, int tenantId, CancellationToken token)
    {
        var connection = await _resolver.GetTenantConnectionAsync(tenantId.ToString(), token);
        if (connection == null) return null;

        await connection.OpenAsync(token);

        using var command = connection.CreateCommand();
        command.CommandText = @"
            SELECT Id, UserId, TenantId, Email, Security, RefreshToken, 
                   TokenExpiry, IsValid, LastSyncTime, CreatedAt, UpdatedAt
            FROM T_UserSyncConfig
            WHERE UserId = @UserId AND TenantId = @TenantId AND IsValid = 1";

        command.Parameters.AddWithValue("@UserId", userId);
        command.Parameters.AddWithValue("@TenantId", tenantId);

        using var reader = await command.ExecuteReaderAsync(token);

        if (await reader.ReadAsync(token))
        {
            return new UserSyncConfig
            {
                Id = reader.GetInt32(0),
                UserId = reader.GetInt32(1),
                TenantId = reader.GetInt32(2),
                Email = reader.GetString(3),
                Security = reader.IsDBNull(4) ? null : reader.GetString(4),
                RefreshToken = reader.IsDBNull(5) ? null : reader.GetString(5),
                TokenExpiry = reader.IsDBNull(6) ? DateTime.MinValue : reader.GetDateTime(6),
                IsValid = reader.GetBoolean(7),
                LastSyncTime = reader.IsDBNull(8) ? null : reader.GetDateTime(8),
                CreatedAt = reader.GetDateTime(9),
                UpdatedAt = reader.GetDateTime(10)
            };
        }

        return null;
    }
}
```

### 5. Graph Service Implementation

```csharp
// GraphService.cs
public class GraphService
{
    private readonly HttpClient _httpClient;
    private readonly TokenService _tokenService;
    private readonly JsonSerializerOptions _jsonOptions;

    public GraphService(TokenService tokenService, IHttpClientFactory httpClientFactory)
    {
        _tokenService = tokenService;
        _httpClient = httpClientFactory.CreateClient("GraphApi");
        _httpClient.BaseAddress = new Uri("https://graph.microsoft.com/v1.0/");
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<List<string>> BatchProcessEventsAsync(
        List<CalendarEvent> events,
        int userId,
        int tenantId,
        CancellationToken cancellationToken)
    {
        var accessToken = await _tokenService.GetAccessTokenAsync(userId, tenantId, cancellationToken);
        if (string.IsNullOrEmpty(accessToken))
        {
            throw new MsalUiRequiredException(
                "TokenNotFound",
                $"Access token not found for user {userId} in tenant {tenantId}");
        }

        // Create batch request with a maximum of 20 operations
        var batchRequests = new List<BatchRequest>();
        var responses = new List<string>();
        
        foreach (var @event in events)
        {
            var eventPayload = new
            {
                subject = @event.Title,
                body = new
                {
                    contentType = "HTML",
                    content = @event.Description ?? string.Empty
                },
                start = new
                {
                    dateTime = @event.StartTime.ToString("o"),
                    timeZone = "UTC"
                },
                end = new
                {
                    dateTime = @event.EndTime.ToString("o"),
                    timeZone = "UTC"
                },
                location = new
                {
                    displayName = @event.Location ?? string.Empty
                }
            };

            var operation = @event.Operation switch
            {
                Operation.Create => "POST",
                Operation.Update => "PATCH",
                Operation.Delete => "DELETE",
                _ => "POST"
            };

            string url = "me/events";
            if (@event.Operation == Operation.Update || @event.Operation == Operation.Delete)
            {
                // For update and delete, we need the event ID
                if (!string.IsNullOrEmpty(@event.ExternalId))
                {
                    url = $"me/events/{@event.ExternalId}";
                }
                else
                {
                    // Skip if we don't have an external ID for update/delete operations
                    continue;
                }
            }

            batchRequests.Add(new BatchRequest
            {
                Id = $"{@event.EventId}",
                Method = operation,
                Url = url,
                Body = operation != "DELETE" ? eventPayload : null
            });
        }

        // Process in batches of 20 (Graph API limit)
        for (int i = 0; i < batchRequests.Count; i += 20)
        {
            var batchSlice = batchRequests.Skip(i).Take(20).ToList();
            var batchResponse = await ExecuteBatchRequestAsync(batchSlice, accessToken, cancellationToken);
            
            if (batchResponse != null)
            {
                responses.AddRange(batchResponse);
            }
        }

        return responses;
    }

    private async Task<List<string>> ExecuteBatchRequestAsync(
        List<BatchRequest> requests, 
        string accessToken,
        CancellationToken cancellationToken)
    {
        var batchPayload = new
        {
            requests = requests
        };

        using var request = new HttpRequestMessage(HttpMethod.Post, "$batch");
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        request.Content = new StringContent(
            JsonSerializer.Serialize(batchPayload, _jsonOptions),
            Encoding.UTF8,
            "application/json");

        try
        {
            using var response = await _httpClient.SendAsync(request, cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync(cancellationToken);
                Log.Error("Graph API batch request failed: {Error}", error);
                return null;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var batchResponse = JsonSerializer.Deserialize<BatchResponse>(content, _jsonOptions);
            
            return batchResponse?.Responses
                .Where(r => r.Status >= 200 && r.Status < 300)
                .Select(r => {
                    if (r.Body != null)
                    {
                        var responseBody = JsonSerializer.Deserialize<GraphEventResponse>(r.Body.ToString(), _jsonOptions);
                        return responseBody?.Id;
                    }
                    return null;
                })
                .Where(id => !string.IsNullOrEmpty(id))
                .ToList();
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error executing Graph API batch request");
            return null;
        }
    }

    private class BatchRequest
    {
        public string Id { get; set; }
        public string Method { get; set; }
        public string Url { get; set; }
        public object Body { get; set; }
    }

    private class BatchResponse
    {
        public List<BatchResponseItem> Responses { get; set; } = new();
    }

    private class BatchResponseItem
    {
        public string Id { get; set; }
        public int Status { get; set; }
        public object Body { get; set; }
    }

    private class GraphEventResponse
    {
        public string Id { get; set; }
    }
}
```

### 6. Calendar Sync Service Implementation

```csharp
// CalendarSyncService.cs
public class CalendarSyncService
{
    private readonly SqlGateway _sqlGateway;
    private readonly GraphService _graphService;
    private readonly TokenService _tokenService;
    private readonly CalendarSyncOptions _options;

    public CalendarSyncService(
        SqlGateway sqlGateway,
        GraphService graphService,
        TokenService tokenService,
        IOptionsMonitor<CalendarSyncOptions> options)
    {
        _sqlGateway = sqlGateway;
        _graphService = graphService;
        _tokenService = tokenService;
        _options = options.CurrentValue;
    }

    public async Task ProcessAppointmentMessage(AppointmentMessage message, CancellationToken token)
    {
        if (message?.Data == null || !message.Data.UserId.HasValue) return;

        var calendarEvent = new CalendarEvent
        {
            EventId = Guid.NewGuid(),
            TenantId = message.Data.FirmId ?? 0,
            UserId = message.Data.UserId.Value,
            Title = message.Data.Appo_Subject ?? "No Title",
            Description = message.Data.Appo_Description,
            Location = message.Data.Appo_Location,
            // Set start/end times - these would come from the appointment data
            StartTime = DateTime.UtcNow,  // This would be extracted from appointment data
            EndTime = DateTime.UtcNow.AddHours(1), // This would be extracted from appointment data
            Operation = message.ToOperation()
        };

        await ProcessCalendarEvent(calendarEvent, token);

        // Update appointment processing status
        await UpdateAppointmentProcessingStatus(
            message.Data.Appo_Id.Value, 
            message.Data.FirmId.Value,
            message.Data.UserId.Value, 
            true, 
            "Processed", 
            null, 
            token);
    }

    public async Task ProcessBatchAsync(List<CalendarEvent> events, CancellationToken token)
    {
        if (events.Count == 0) return;

        try
        {
            // Group events by user to ensure we have the right access token for each user
            var eventsByUser = events.GroupBy(e => new { e.UserId, e.TenantId });

            foreach (var userGroup in eventsByUser)
            {
                var userId = userGroup.Key.UserId;
                var tenantId = userGroup.Key.TenantId;
                var userEvents = userGroup.ToList();

                try
                {
                    // Process events with Graph API
                    var graphResponses = await _graphService.BatchProcessEventsAsync(
                        userEvents, userId, tenantId, token);

                    // Update external IDs from Graph responses
                    if (graphResponses != null && graphResponses.Count > 0)
                    {
                        for (int i = 0; i < Math.Min(userEvents.Count, graphResponses.Count); i++)
                        {
                            userEvents[i].ExternalId = graphResponses[i];
                            userEvents[i].ExternalSystem = "GraphAPI";
                        }
                    }

                    // Store events in database
                    await _sqlGateway.BatchCreateAsync(userEvents, token);
                }
                catch (MsalUiRequiredException ex)
                {
                    // Handle authentication errors by marking the token as invalid
                    await _tokenService.InvalidateTokenAsync(userId, tenantId, token);

                    Log.Error(ex, "Authentication error for user {UserId}. Token has been invalidated.", userId);
                    
                    // Update appointment processing statuses
                    foreach (var evt in userEvents)
                    {
                        await UpdateAppointmentProcessingStatus(
                            Convert.ToInt32(evt.EventId.ToString().Split('-')[0]), // Extract appointment ID
                            evt.TenantId,
                            evt.UserId,
                            false,
                            "Failed",
                            "Authentication error: " + ex.Message,
                            token);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error processing batch of {Count} events for user {UserId}", userEvents.Count, userId);
                    
                    // Update appointment processing statuses
                    foreach (var evt in userEvents)
                    {
                        await UpdateAppointmentProcessingStatus(
                            Convert.ToInt32(evt.EventId.ToString().Split('-')[0]), // Extract appointment ID
                            evt.TenantId,
                            evt.UserId,
                            false,
                            "Failed",
                            "Error: " + ex.Message,
                            token);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error processing batch of {Count} events", events.Count);
            throw;
        }
    }

    private async Task ProcessCalendarEvent(CalendarEvent calendarEvent, CancellationToken token)
    {
        try
        {
            var events = new List<CalendarEvent> { calendarEvent };
            await ProcessBatchAsync(events, token);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error processing calendar event {EventId}", calendarEvent.EventId);
            throw;
        }
    }

    private async Task UpdateAppointmentProcessingStatus(
        int appointmentId, 
        int firmId, 
        int userId,
        bool isProcessed,
        string status,
        string errorMessage,
        CancellationToken token)
    {
        try
        {
            var connection = await _sqlGateway.GetConnectionForTenant(firmId, token);
            if (connection == null) return;

            await connection.OpenAsync(token);

            using var command = connection.CreateCommand();
            command.CommandText = @"
                MERGE INTO AppointmentProcessingStatus AS target
                USING (SELECT @AppointmentId, @FirmId, @UserId) AS source (AppointmentId, FirmId, UserId)
                ON (target.AppointmentId = source.AppointmentId AND target.FirmId = source.FirmId)
                WHEN MATCHED THEN
                    UPDATE SET 
                        IsProcessed = @IsProcessed,
                        Status = @Status,
                        ErrorMessage = @ErrorMessage,
                        ProcessedAt = @ProcessedAt,
                        RetryCount = target.RetryCount + 1
                WHEN NOT MATCHED THEN
                    INSERT (AppointmentId, FirmId, UserId, IsProcessed, Status, ErrorMessage, CreatedAt, ProcessedAt, RetryCount)
                    VALUES (@AppointmentId, @FirmId, @UserId, @IsProcessed, @Status, @ErrorMessage, @CreatedAt, @ProcessedAt, 1);";

            command.Parameters.AddWithValue("@AppointmentId", appointmentId);
            command.Parameters.AddWithValue("@FirmId", firmId);
            command.Parameters.AddWithValue("@UserId", userId);
            command.Parameters.AddWithValue("@IsProcessed", isProcessed);
            command.Parameters.AddWithValue("@Status", status);
            command.Parameters.AddWithValue("@ErrorMessage", errorMessage ?? (object)DBNull.Value);
            command.Parameters.AddWithValue("@CreatedAt", DateTime.UtcNow);
            command.Parameters.AddWithValue("@ProcessedAt", isProcessed ? DateTime.UtcNow : (object)DBNull.Value);

            await command.ExecuteNonQueryAsync(token);

            Log.Information("Updated processing status for appointment {AppointmentId} in firm {FirmId}: {Status}",
                appointmentId, firmId, status);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error updating processing status for appointment {AppointmentId} in firm {FirmId}",
                appointmentId, firmId);
        }
    }
}
```

### 7. Worker Implementation

```csharp
// Worker.cs
public class Worker : BackgroundService
{
    private readonly ConsumerService _consumer;
    private readonly CalendarSyncService _syncService;
    private readonly CalendarSyncOptions _options;

    public Worker(ConsumerService consumer, CalendarSyncService syncService, CalendarSyncOptions options)
    {
        _consumer = consumer;
        _syncService = syncService;
        _options = options;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var cancellation = new CancellationTokenSource(_options.CommitIntervalMs).Token;
            var tasks = new List<Task>();

            // Define topics to consume
            var topics = new[] { "calendar-events" };

            foreach (var topic in topics)
            {
                tasks.Add(Task.Run(async () =>
                {
                    await _consumer.ConsumeAsync(
                        ProcessAppointmentMessagesWithBatchingAsync,
                        topic,
                        "calendar-sync-group",
                        cancellation);
                }, stoppingToken));
            }

            await Task.WhenAll(tasks);
        }
    }

    private async ValueTask ProcessAppointmentMessagesWithBatchingAsync(
        IAsyncEnumerable<ConsumeResult<MessageKey, AppointmentMessage>> messages,
        CancellationToken token)
    {
        var batch = new List<CalendarEvent>();

        await foreach (var result in messages.WithCancellation(token))
        {
            try
            {
                var message = result.Message.Value;
                
                if (message?.Data == null || !message.Data.UserId.HasValue || !message.Data.Appo_Id.HasValue)
                {
                    Log.Warning("Received invalid appointment message: {MessageId}", result.Message.Key);
                    continue;
                }

                // Convert appointment message to calendar event
                var calendarEvent = new CalendarEvent
                {
                    EventId = Guid.NewGuid(),
                    TenantId = message.Data.FirmId ?? 0,
                    UserId = message.Data.UserId.Value,
                    Title = message.Data.Appo_Subject ?? "No Title",
                    Description = message.Data.Appo_Description,
                    Location = message.Data.Appo_Location,
                    // Set appropriate start/end times from appointment data
                    StartTime = DateTime.UtcNow, // This should come from appointment data
                    EndTime = DateTime.UtcNow.AddHours(1), // This should come from appointment data
                    Operation = message.ToOperation()
                };
                
                batch.Add(calendarEvent);

                // Process batch when it reaches the configured size
                if (batch.Count >= _options.BatchSize)
                {
                    try
                    {
                        await _syncService.ProcessBatchAsync(batch, token);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "Error processing batch of {Count} events", batch.Count);
                    }
                    finally
                    {
                        batch.Clear();
                    }
                }
                
                // Store offset
                _consumer.StoreOffset(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing appointment message");
            }
        }

        // Process any remaining events
        if (batch.Count > 0)
        {
            try
            {
                await _syncService.ProcessBatchAsync(batch, token);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error processing final batch of {Count} events", batch.Count);
            }
        }
    }
}
```

### 8. Database Schema Updates

In addition to the previously defined tables, add the following table to track appointment processing status:

```sql
CREATE TABLE AppointmentProcessingStatus (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    AppointmentId INT NOT NULL,
    FirmId INT NOT NULL,
    UserId INT NOT NULL,
    IsProcessed BIT NOT NULL DEFAULT 0,
    Status NVARCHAR(50) NOT NULL, -- "Pending", "Processed", "Failed"
    ErrorMessage NVARCHAR(MAX),
    GraphResponseId NVARCHAR(255),
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ProcessedAt DATETIME2,
    RetryCount INT NOT NULL DEFAULT 0,
    CONSTRAINT UQ_AppointmentProcessingStatus UNIQUE(AppointmentId, FirmId)
);

CREATE INDEX IX_AppointmentProcessingStatus_Status ON AppointmentProcessingStatus(Status);
CREATE INDEX IX_AppointmentProcessingStatus_UserId ON AppointmentProcessingStatus(UserId);
```

## Program Setup

```csharp
// Program.cs
public class Program
{
    public static async Task Main(string[] args)
    {
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
            .Enrich.FromLogContext()
            .WriteTo.Console()
            .WriteTo.File("logs/calendar-sync-.log", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        try
        {
            Log.Information("Starting Calendar Sync Service");
            await CreateHostBuilder(args).Build().RunAsync();
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "Calendar Sync Service terminated unexpectedly");
        }
        finally
        {
            Log.CloseAndFlush();
        }
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .UseSerilog()
            .ConfigureServices((hostContext, services) =>
            {
                // Register options
                services.Configure<CalendarSyncOptions>(
                    hostContext.Configuration.GetSection(nameof(CalendarSyncOptions)));
                services.Configure<KafkaOptions>(
                    hostContext.Configuration.GetSection(nameof(KafkaOptions)));
                services.Configure<SystemDbOptions>(
                    hostContext.Configuration.GetSection(nameof(SystemDbOptions)));

                // Register schema registry client
                services.AddSingleton<ISchemaRegistryClient>(provider =>
                {
                    return new CachedSchemaRegistryClient(new SchemaRegistryConfig
                    {
                        Url = hostContext.Configuration.GetConnectionString("SchemaRegistry")
                    });
                });

                // Register HTTP client for Graph API calls
                services.AddHttpClient("GraphApi", client =>
                {
                    client.BaseAddress = new Uri("https://graph.microsoft.com/v1.0/");
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                });

                // Register services
                services.AddMemoryCache();
                services.AddSingleton<TenantResolver>();
                services.AddSingleton<TokenService>();
                services.AddSingleton<GraphService>();
                services.AddSingleton<SqlGateway>();
                services.AddSingleton<ConsumerFactory<MessageKey, AppointmentMessage>>();
                services.AddSingleton<ConsumerService>();
                services.AddSingleton<CalendarSyncService>();

                // Register hosted service
                services.AddHostedService<Worker>();
            });
}
```

## Key Features Implemented

1. **Avro Message Processing**: The service can deserialize Avro-formatted messages with proper error handling.
2. **User Token Management**: Tokens are retrieved from the T_UserSyncConfig table, cached, and refreshed as needed.
3. **Batched Graph API Calls**: Calendar events are processed in batches of up to 20 operations per request, following Microsoft Graph API limits.
4. **User-based Grouping**: Events are grouped by user to ensure proper token usage for each user.
5. **Status Tracking**: Every appointment's processing status is tracked in the AppointmentProcessingStatus table.
6. **Error Handling**: Robust error handling with token invalidation when authentication fails.
7. **Resilience**: The system can recover from transient failures and continue processing.
