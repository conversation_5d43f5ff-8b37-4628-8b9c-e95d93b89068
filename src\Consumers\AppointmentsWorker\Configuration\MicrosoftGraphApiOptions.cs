using System;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration
{
    public class MicrosoftGraphApiOptions
    {
        public string Login { get; set; } = "https://login.microsoftonline.com";
        
        public string GraphUrlBase { get; set; } = "https://graph.microsoft.com/v1.0";
        
        public string ClientId { get; set; } = string.Empty;
        
        public string ClientSecret { get; set; } = string.Empty;
        
        public string[] GraphScopes { get; set; } = Array.Empty<string>();
        
        public string GraphRedirectUrl { get; set; } = string.Empty;
        
        public string RedirectUrl { get; set; } = string.Empty;
        
        public string TenantId { get; set; } = "common";
    }
}
