using System;
using FluentValidation;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Validation
{
    /// <summary>
    /// Validator for BatchOperation objects
    /// </summary>
    public class BatchOperationValidator : AbstractValidator<BatchOperation>
    {
        public BatchOperationValidator()
        {
            // Basic properties validation
            RuleFor(x => x.ItemId)
                .NotEmpty().WithMessage("ItemId is required");

            RuleFor(x => x.OperationType)
                .IsInEnum().WithMessage("OperationType must be a valid batch operation type");

            // Validate URL format for all operations
            RuleFor(x => x.Url)
                .NotEmpty().WithMessage("URL is required")
                .Must(BeValidUrl).WithMessage("URL must be a valid format");

            // HTTP method validation based on operation type
            RuleFor(x => x.Method)
                .NotEmpty().WithMessage("Method is required")
                .Must((batch, method) => IsValidMethodForOperation(batch.OperationType, method))
                .WithMessage("Method must be appropriate for the operation type");

            // Body validation - required for create/update, not for delete
            When(x => x.OperationType == BatchOperationType.Create || x.OperationType == BatchOperationType.Update, () => {
                RuleFor(x => x.Body)
                    .NotEmpty().WithMessage("Body is required for create and update operations");
            });

            // JSON body validation when present
            When(x => !string.IsNullOrEmpty(x.Body), () => {
                RuleFor(x => x.Body)
                    .Must(BeValidJson).WithMessage("Body must be valid JSON");
            });
        }

        /// <summary>
        /// Validates that a string is a valid URL
        /// </summary>
        private bool BeValidUrl(string? url)
        {
            if (string.IsNullOrEmpty(url))
                return false;

            return Uri.TryCreate(url, UriKind.Absolute, out _);
        }

        /// <summary>
        /// Validates that the HTTP method matches the operation type
        /// </summary>
        private bool IsValidMethodForOperation(BatchOperationType operationType, string method)
        {
            return operationType switch
            {
                BatchOperationType.Create => method.Equals("POST", StringComparison.OrdinalIgnoreCase),
                BatchOperationType.Update => method.Equals("PATCH", StringComparison.OrdinalIgnoreCase),
                BatchOperationType.Delete => method.Equals("DELETE", StringComparison.OrdinalIgnoreCase),
                _ => false
            };
        }

        /// <summary>
        /// Validates that a string is valid JSON
        /// </summary>
        private bool BeValidJson(string json)
        {
            try
            {
                Newtonsoft.Json.Linq.JToken.Parse(json);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
