using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Formatting.Compact;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using OpenTelemetry.Metrics;
using OpenTelemetry.Logs;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Serialization;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Gateways;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Contexts;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Validation;
using Polly;
using Polly.Extensions.Http;
using Microsoft.EntityFrameworkCore;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker
{
    public class Program
    {
        private const string ServiceName = "AppointmentsConsumer";
        
        public static async Task Main(string[] args)
        {
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .Enrich.FromLogContext()
                .Enrich.WithProperty("Application", ServiceName)
                .WriteTo.Console(new CompactJsonFormatter())
                .CreateBootstrapLogger();

            try
            {
                Log.Information("Starting {ServiceName} service", ServiceName);
                
                await CreateHostBuilder(args).Build().RunAsync();
                
                Log.Information("{ServiceName} service terminated successfully", ServiceName);
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "{ServiceName} service terminated unexpectedly", ServiceName);
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)                .ConfigureAppConfiguration((hostingContext, config) =>
                {
                    var env = hostingContext.HostingEnvironment;
                    
                    config.SetBasePath(Directory.GetCurrentDirectory())
                          .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                          .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true)
                          .AddEnvironmentVariables();
                          
                    Console.WriteLine($"Environment: {env.EnvironmentName}");
                })
                .UseSerilog((hostingContext, services, loggerConfiguration) =>
                {                    loggerConfiguration
                        .ReadFrom.Configuration(hostingContext.Configuration)
                        .ReadFrom.Services(services)
                        .Enrich.FromLogContext()
                        .Enrich.WithProperty("Application", ServiceName)
                        .Enrich.WithProperty("Environment", hostingContext.HostingEnvironment.EnvironmentName);
                    if (hostingContext.Configuration.GetValue<bool>("Datadog:Enabled"))
                    {
                        loggerConfiguration.WriteTo.DatadogLogs(
                            hostingContext.Configuration.GetValue<string>("Datadog:ApiKey"),
                            source: ServiceName,
                            service: ServiceName,
                            host: Environment.MachineName,
                            configuration: new Serilog.Sinks.Datadog.Logs.DatadogConfiguration
                            {
                                Url = hostingContext.Configuration.GetValue<string>("Datadog:Url") ?? "https://http-intake.logs.datadoghq.com"
                            });
                    }
                })                .ConfigureServices((hostContext, services) =>
                {
                    // Add configuration options
                    services.Configure<ApplicationOptions>(hostContext.Configuration.GetSection("Application"));
                    services.Configure<KafkaOptions>(hostContext.Configuration.GetSection("Kafka"));
                    services.Configure<DatabaseOptions>(hostContext.Configuration.GetSection("Database"));
                    services.Configure<MicrosoftGraphApiOptions>(hostContext.Configuration.GetSection("MicrosoftGraphApi"));
                    
                    // Configure JSON serialization (Newtonsoft.Json)
                    ConfigureJsonSerialization();
                    
                    // Add FluentValidation validators
                    services.AddAppointmentsConsumerValidation();

                    // Add OpenTelemetry for distributed tracing and metrics
                    ConfigureOpenTelemetry(services, hostContext);

                    // Add HTTP clients with resilience policies
                    ConfigureHttpClients(services, hostContext);

                    // Add database contexts
                    ConfigureDatabases(services, hostContext);                    // Register message channel for buffering
                    services.AddSingleton(provider =>
                    {
                        var capacity = hostContext.Configuration.GetValue<int>("MessageChannel:Capacity", 1000);
                        return new MessageChannel<AppointmentMessage>(capacity);
                    });
                    
                    // Register UserBatchChannel for user-based batching
                    services.AddSingleton<UserBatchChannel>();
                      // Add Schema Registry
                    services.AddSingleton<Confluent.SchemaRegistry.ISchemaRegistryClient>(provider =>
                    {
                        var kafkaOptions = provider.GetRequiredService<IOptions<KafkaOptions>>().Value;
                        var schemaRegistryConfig = new Confluent.SchemaRegistry.SchemaRegistryConfig
                        {
                            Url = kafkaOptions.SchemaRegistryUrl
                        };
                        
                        if (kafkaOptions.UseSecureConnection && !string.IsNullOrEmpty(kafkaOptions.SchemaRegistryUsername))
                        {
                            schemaRegistryConfig.BasicAuthCredentialsSource = Confluent.SchemaRegistry.AuthCredentialsSource.UserInfo;
                            schemaRegistryConfig.BasicAuthUserInfo = $"{kafkaOptions.SchemaRegistryUsername}:{kafkaOptions.SchemaRegistryPassword}";
                        }
                        
                        return new Confluent.SchemaRegistry.CachedSchemaRegistryClient(schemaRegistryConfig);
                    });
                    
                    // Add services
                    services.AddSingleton<KafkaConsumerFactory>();
                    services.AddSingleton<AvroMessageDeserializer>();
                    services.AddSingleton<MessageReplayService>();
                    services.AddScoped<DatabaseGateway>();
                    services.AddScoped<TenantConnectionFactory>();
                    services.AddSingleton<TokenManagementService>();
                    services.AddSingleton<GraphApiService>();
                    services.AddSingleton<BatchProcessor>();

                    // Add hosted services
                    services.AddHostedService<AppointmentsHostedService>();
                });

        /// <summary>
        /// Configures OpenTelemetry for distributed tracing and metrics
        /// with DataDog and other exporters.
        /// </summary>
        private static void ConfigureOpenTelemetry(IServiceCollection services, HostBuilderContext hostContext)
        {
            var resourceBuilder = ResourceBuilder.CreateDefault()
                .AddService(ServiceName)
                .AddTelemetrySdk()
                .AddEnvironmentVariableDetector();

            // Configure tracing
            services.AddOpenTelemetryTracing(builder =>
            {
                builder
                    .SetResourceBuilder(resourceBuilder)
                    .AddSource(ServiceName)
                    .AddHttpClientInstrumentation()
                    .AddEntityFrameworkCoreInstrumentation(options =>
                    {
                        options.SetDbStatementForText = true;
                    })
                    .AddConsoleExporter();

                // Add DataDog exporter if enabled
                if (hostContext.Configuration.GetValue<bool>("Datadog:Enabled"))
                {
                    builder.AddDatadogExporter(options =>
                    {
                        options.AgentHost = hostContext.Configuration.GetValue<string>("Datadog:AgentHost") ?? "localhost";
                        options.AgentPort = hostContext.Configuration.GetValue<int>("Datadog:AgentPort", 8126);
                        options.ServiceName = ServiceName;
                        options.Environment = hostContext.HostingEnvironment.EnvironmentName;
                    });
                }
            });

            // Configure metrics
            services.AddOpenTelemetryMetrics(builder =>
            {
                builder
                    .SetResourceBuilder(resourceBuilder)
                    .AddHttpClientInstrumentation()
                    .AddProcessInstrumentation()
                    .AddRuntimeInstrumentation()
                    .AddConsoleExporter();

                // Add DataDog exporter if enabled
                if (hostContext.Configuration.GetValue<bool>("Datadog:Enabled"))
                {
                    builder.AddDatadogExporter(options =>
                    {
                        options.AgentHost = hostContext.Configuration.GetValue<string>("Datadog:AgentHost") ?? "localhost";
                        options.AgentPort = hostContext.Configuration.GetValue<int>("Datadog:AgentPort", 8125);
                        options.ServiceName = ServiceName;
                        options.Environment = hostContext.HostingEnvironment.EnvironmentName;
                    });
                }
            });

            // Configure logging
            services.AddLogging(builder =>
            {
                builder.AddOpenTelemetry(options =>
                {
                    var resourceBuilder = ResourceBuilder.CreateDefault()
                        .AddService(ServiceName);

                    options.SetResourceBuilder(resourceBuilder)
                           .AddConsoleExporter();

                    // Add DataDog exporter if enabled
                    if (hostContext.Configuration.GetValue<bool>("Datadog:Enabled"))
                    {
                        options.AddDatadogExporter(options =>
                        {
                            options.ApiKey = hostContext.Configuration.GetValue<string>("Datadog:ApiKey");
                            options.ServiceName = ServiceName;
                        });
                    }
                });
            });
        }        /// <summary>
        /// Configures HTTP clients with resilience policies
        /// </summary>
        private static void ConfigureHttpClients(IServiceCollection services, HostBuilderContext hostContext)
        {
            var graphOptions = hostContext.Configuration.GetSection("MicrosoftGraphApi").Get<MicrosoftGraphApiOptions>() 
                ?? new MicrosoftGraphApiOptions();
            
            // Create resilience policies
            var standardRetryPolicy = HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));

            var circuitBreakerPolicy = HttpPolicyExtensions
                .HandleTransientHttpError()
                .CircuitBreakerAsync(5, TimeSpan.FromMinutes(1));

            // Add HTTP clients with policies
            services.AddHttpClient("GraphApi", client =>
                {
                    client.BaseAddress = new Uri(graphOptions.GraphUrlBase);
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                    client.Timeout = TimeSpan.FromSeconds(30);
                })
                .AddPolicyHandler(standardRetryPolicy)
                .AddPolicyHandler(circuitBreakerPolicy);
        }

        /// <summary>
        /// Configures database contexts with proper options
        /// </summary>
        private static void ConfigureDatabases(IServiceCollection services, HostBuilderContext hostContext)
        {
            // Get database options from configuration
            var dbOptions = hostContext.Configuration.GetSection("Database").Get<DatabaseOptions>();

            // Add the system database context
            services.AddDbContext<SystemDbContext>((provider, options) =>
            {
                options.UseSqlServer(dbOptions.SystemConnectionString, sqlOptions =>
                {
                    sqlOptions.EnableRetryOnFailure(
                        dbOptions.MaxRetries, 
                        TimeSpan.FromSeconds(dbOptions.RetryDelaySeconds), 
                        null);
                    sqlOptions.CommandTimeout(dbOptions.CommandTimeoutSeconds);
                });
            });
        }

        /// <summary>
        /// Configure global JSON serialization settings
        /// </summary>
        private static void ConfigureJsonSerialization()
        {
            // Configure Newtonsoft.Json default settings globally
            Newtonsoft.Json.JsonConvert.DefaultSettings = () => new Newtonsoft.Json.JsonSerializerSettings
            {
                NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore,
                DateFormatHandling = Newtonsoft.Json.DateFormatHandling.IsoDateFormat,
                DateTimeZoneHandling = Newtonsoft.Json.DateTimeZoneHandling.Utc,
                ContractResolver = new Newtonsoft.Json.Serialization.CamelCasePropertyNamesContractResolver(),
                ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
            };
        }
    }
}


