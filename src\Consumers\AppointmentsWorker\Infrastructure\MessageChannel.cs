using System;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Infrastructure
{
    /// <summary>
    /// Channel for buffering appointment messages between Kafka consumer and processor
    /// </summary>
    public class MessageChannel
    {
        private readonly Channel<AppointmentMessage> _channel;
        private readonly ILogger<MessageChannel> _logger;
        private readonly ApplicationOptions _options;
        
        /// <summary>
        /// Gets the current number of messages in the channel
        /// </summary>
        public int Count => _channel.Reader.CanCount ? _channel.Reader.Count : -1;
        
        /// <summary>
        /// Gets the maximum capacity of the channel
        /// </summary>
        public int Capacity { get; }
        
        /// <summary>
        /// Returns true if the channel is empty
        /// </summary>
        public bool IsEmpty => Count == 0;
        
        /// <summary>
        /// Returns true if the channel is full
        /// </summary>
        public bool IsFull => Count >= Capacity;
        
        /// <summary>
        /// Creates a new instance of MessageChannel
        /// </summary>
        /// <param name="logger">Logger</param>
        /// <param name="options">Application options</param>
        public MessageChannel(ILogger<MessageChannel> logger, IOptions<ApplicationOptions> options)
        {
            _logger = logger;
            _options = options.Value;
            Capacity = _options.MaxQueueSize;
            
            // Create a bounded channel with specified capacity and wait mode when full
            _channel = Channel.CreateBounded<AppointmentMessage>(
                new BoundedChannelOptions(Capacity)
                {
                    FullMode = BoundedChannelFullMode.Wait,
                    SingleReader = false,
                    SingleWriter = false,
                    AllowSynchronousContinuations = false
                });
            
            _logger.LogInformation("Created message channel with capacity: {Capacity}", Capacity);
        }
        
        /// <summary>
        /// Writes a message to the channel
        /// </summary>
        /// <param name="message">Message to write</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Task that completes when the message is written</returns>
        public async ValueTask WriteAsync(AppointmentMessage message, CancellationToken cancellationToken = default)
        {
            if (message == null)
            {
                throw new ArgumentNullException(nameof(message));
            }
            
            try
            {
                await _channel.Writer.WriteAsync(message, cancellationToken);
                
                if (Count % 100 == 0) // Log every 100 messages to avoid excessive logging
                {
                    _logger.LogDebug("Channel status: {Count}/{Capacity} messages queued", Count, Capacity);
                }
            }
            catch (Exception ex) when (ex is ChannelClosedException || ex is OperationCanceledException)
            {
                // These exceptions are expected during shutdown
                _logger.LogDebug(ex, "Channel write operation canceled or channel closed");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing message to channel");
                throw;
            }
        }
        
        /// <summary>
        /// Tries to read a message from the channel with a timeout
        /// </summary>
        /// <param name="message">Output message if successful</param>
        /// <param name="timeout">Timeout duration</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if a message was read, false if timeout occurred</returns>
        public bool TryRead(out AppointmentMessage? message, TimeSpan timeout, CancellationToken cancellationToken = default)
        {
            try
            {
                // Create a timeout cancellation source
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                timeoutCts.CancelAfter(timeout);
                
                // Try to read until timeout or cancellation
                if (_channel.Reader.TryRead(out message))
                {
                    return true;
                }
                
                // If channel is empty, wait for a message
                try
                {
                    // WaitToReadAsync will wait until a message is available or the token is canceled
                    var waitTask = _channel.Reader.WaitToReadAsync(timeoutCts.Token).AsTask();
                    waitTask.Wait(timeoutCts.Token);
                    
                    // If wait completed, try to read again
                    return _channel.Reader.TryRead(out message);
                }
                catch (OperationCanceledException) when (timeoutCts.IsCancellationRequested)
                {
                    // Timeout occurred
                    message = null;
                    return false;
                }
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                // Operation was canceled
                message = null;
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading message from channel");
                message = null;
                return false;
            }
        }
        
        /// <summary>
        /// Completes the channel, preventing further writes
        /// </summary>
        /// <param name="exception">Optional exception that caused completion</param>
        public void Complete(Exception? exception = null)
        {
            if (exception != null)
            {
                _logger.LogWarning(exception, "Completing message channel due to exception");
                _channel.Writer.Complete(exception);
            }
            else
            {
                _logger.LogInformation("Completing message channel");
                _channel.Writer.Complete();
            }
        }
        
        /// <summary>
        /// Applies backpressure based on current channel capacity
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        public async ValueTask ApplyBackpressureAsync(CancellationToken cancellationToken)
        {
            if (Count <= 0) return;
            
            var ratio = (double)Count / Capacity;
            var delayMs = (int)(ratio * _options.RetryBaseDelayMs);
            
            if (delayMs <= 0) return;
            
            await Task.Delay(delayMs, cancellationToken);
        }
    }
}
