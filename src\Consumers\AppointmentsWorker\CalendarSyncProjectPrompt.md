# Calendar Event Synchronization Project Prompt

## Project Description

Create a distributed, scalable background service for synchronizing calendar events between different systems. The service should:

1. Consume calendar event changes from Kafka
2. Process events in batches (configurable, default 100)
3. Perform Create, Update, Delete operations in a SQL database
4. Support multi-tenancy
5. Be highly scalable and resilient

## Architecture Requirements

Based on the DynamicIndexer project architecture:

1. **Worker Service**: Implement a background service that consumes messages from Kafka topics and processes them concurrently.

2. **Message Bus**: Create a channel-based system for decoupling Kafka consumption from processing with backpressure support.

3. **Batch Processing**: Implement batching of database operations for efficiency.

4. **Error Handling**: Implement robust error handling with retry mechanisms.

5. **Configuration**: Make all important parameters configurable.

6. **Authentication**: Implement token management for accessing calendar APIs:
   - Retrieve access tokens from the T_UserSyncConfig table's Security column
   - Handle MsalUiRequiredException by marking tokens as invalid in the database
   - Cache tokens for efficient reuse

## Technical Implementation Details

### 1. Project Structure

```
Caret.CalendarSync/
├── src/
│   ├── Consumers/
│   │   └── WorkerService/
│   │       ├── Contexts/
│   │       │   ├── TenantResolver.cs
│   │       │   └── SystemDbContext.cs
│   │       ├── Factories/
│   │       │   └── ConsumerFactory.cs
│   │       ├── Gateways/
│   │       │   └── SqlGateway.cs
│   │       ├── Infrastructure/
│   │       │   ├── MessageBus.cs
│   │       │   └── CalendarSyncDefinition.cs
│   │       ├── Models/
│   │       │   └── CalendarEvent.cs
│   │       ├── Services/
│   │       │   ├── ConsumerService.cs
│   │       │   └── CalendarSyncService.cs
│   │       ├── ValueObjects/
│   │       │   └── Message.cs
│   │       ├── Options.cs
│   │       ├── Program.cs
│   │       └── Worker.cs
│   └── Producers/ (optional for testing)
└── tests/
```

### 2. Key Components

#### CalendarEvent Model

```csharp
public class CalendarEvent
{
    public Guid EventId { get; set; }
    public int TenantId { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Location { get; set; }
    public string Organizer { get; set; }
    public List<string> Attendees { get; set; }
    public string RecurrenceRule { get; set; }
    public bool IsAllDay { get; set; }
    public string Status { get; set; }
    public string ExternalId { get; set; }
    public string ExternalSystem { get; set; }
    public Operation Operation { get; set; }
}

public enum Operation
{
    Create,
    Update,
    Delete
}
```

#### MessageBus

Implement a channel-based message bus similar to DynamicIndexer that:
- Creates a bounded channel with configurable capacity
- Consumes messages from Kafka and writes to the channel
- Provides a subscription mechanism for consumers
- Implements backpressure to handle high message volumes
- Handles error resilience and offset management

#### CalendarSyncService

Implement a service that:
- Processes calendar events in batches
- Groups events by operation type (Create, Update, Delete)
- Calls the SqlGateway to perform batched database operations
- Handles errors and retries

#### SqlGateway

Implement a gateway that:
- Manages database connections using a TenantResolver
- Executes SQL queries for batch operations
- Uses efficient techniques like SqlBulkCopy or table-valued parameters
- Handles transactions and error conditions

#### Worker

Implement a background service that:
- Consumes messages from Kafka topics
- Creates processing tasks with configurable concurrency
- Manages the lifecycle of the service

#### TokenService

Implement a service that:
- Retrieves access tokens from the T_UserSyncConfig table
- Extracts the token from the Security column (JSON format)
- Caches tokens for efficient reuse
- Handles token invalidation when authentication errors occur
- Updates the database when tokens become invalid

### 3. Configuration

Make the following parameters configurable:

```csharp
public record CalendarSyncOptions
{
    [Required] public int BatchSize { get; init; } = 100;
    [Required] public int ChannelCapacity { get; init; } = 10000;
    [Required] public int Concurrency { get; init; } = 4;
    [Required] public int CacheExpirationMin { get; init; } = 15;
    [Required] public int ConsumeTimeoutMs { get; init; } = 10000;
    [Required] public double MaxBackoffDelayMs { get; init; } = 5000;
}
```

### 4. Database Schema

Create a SQL database schema for calendar events:

```sql
CREATE TABLE CalendarEvents (
    EventId UNIQUEIDENTIFIER PRIMARY KEY,
    TenantId INT NOT NULL,
    UserId INT NOT NULL,
    Title NVARCHAR(255) NOT NULL,
    Description NVARCHAR(MAX),
    StartTime DATETIME2 NOT NULL,
    EndTime DATETIME2 NOT NULL,
    Location NVARCHAR(255),
    Organizer NVARCHAR(255),
    Attendees NVARCHAR(MAX),
    RecurrenceRule NVARCHAR(255),
    IsAllDay BIT NOT NULL DEFAULT 0,
    Status NVARCHAR(50),
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ExternalId NVARCHAR(255),
    ExternalSystem NVARCHAR(100),
    IsDeleted BIT NOT NULL DEFAULT 0
);

CREATE TABLE T_UserSyncConfig (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    TenantId INT NOT NULL,
    Email NVARCHAR(255) NOT NULL,
    Security NVARCHAR(MAX), -- Contains AccessToken
    RefreshToken NVARCHAR(MAX),
    TokenExpiry DATETIME2,
    IsValid BIT NOT NULL DEFAULT 1,
    LastSyncTime DATETIME2,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE()
);
```

Create a table type for batch operations:

```sql
CREATE TYPE dbo.CalendarEventTableType AS TABLE (
    EventId UNIQUEIDENTIFIER,
    TenantId INT,
    Title NVARCHAR(255),
    Description NVARCHAR(MAX),
    StartTime DATETIME2,
    EndTime DATETIME2,
    Location NVARCHAR(255),
    Organizer NVARCHAR(255),
    Attendees NVARCHAR(MAX),
    RecurrenceRule NVARCHAR(255),
    IsAllDay BIT,
    Status NVARCHAR(50),
    ExternalId NVARCHAR(255),
    ExternalSystem NVARCHAR(100)
);
```

Create stored procedures for batch operations:

```sql
CREATE PROCEDURE UpdateCalendarEvents
    @EventsTable dbo.CalendarEventTableType READONLY
AS
BEGIN
    SET NOCOUNT ON;

    MERGE INTO CalendarEvents AS target
    USING @EventsTable AS source
    ON target.EventId = source.EventId
    WHEN MATCHED THEN
        UPDATE SET
            Title = source.Title,
            Description = source.Description,
            StartTime = source.StartTime,
            EndTime = source.EndTime,
            Location = source.Location,
            Organizer = source.Organizer,
            Attendees = source.Attendees,
            RecurrenceRule = source.RecurrenceRule,
            IsAllDay = source.IsAllDay,
            Status = source.Status,
            UpdatedAt = GETUTCDATE(),
            ExternalId = source.ExternalId,
            ExternalSystem = source.ExternalSystem
    WHEN NOT MATCHED THEN
        INSERT (EventId, TenantId, Title, Description, StartTime, EndTime, Location, Organizer,
                Attendees, RecurrenceRule, IsAllDay, Status, ExternalId, ExternalSystem)
        VALUES (source.EventId, source.TenantId, source.Title, source.Description, source.StartTime,
                source.EndTime, source.Location, source.Organizer, source.Attendees,
                source.RecurrenceRule, source.IsAllDay, source.Status, source.ExternalId, source.ExternalSystem);
END;
```

### 5. Batch Processing Implementation

Implement batch processing using the following approach:

1. Collect events in a list until the batch size is reached
2. Group events by operation type (Create, Update, Delete)
3. Process each group in parallel using the SqlGateway
4. Use efficient database operations (SqlBulkCopy, table-valued parameters)
5. Handle transactions and error conditions

### 6. Error Handling and Resilience

Implement robust error handling:

1. Catch and log exceptions
2. Implement retry mechanisms for transient errors
3. Use circuit breakers for external dependencies
4. Handle Kafka errors gracefully
5. Implement dead-letter queues for failed messages

### 7. Monitoring and Logging

Implement comprehensive logging and monitoring:

1. Log all important events and errors
2. Track metrics for batch sizes, processing times, and error rates
3. Implement health checks
4. Use structured logging for better analysis

## Deliverables

1. A complete, working .NET solution for the Calendar Event Synchronization service
2. SQL scripts for creating the necessary database objects
3. Configuration files for different environments
4. Documentation on how to deploy and operate the service
5. Unit and integration tests

## Technical Constraints

1. Use .NET 7.0 or later
2. Use Confluent.Kafka for Kafka integration
3. Use Microsoft.Data.SqlClient for database access
4. Use Microsoft.Extensions.Hosting for the worker service
5. Use System.Threading.Channels for the message bus
