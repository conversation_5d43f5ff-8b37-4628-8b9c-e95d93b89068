using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models
{
    /// <summary>
    /// Represents a batch operation for the Microsoft Graph API
    /// </summary>
    public class BatchOperation
    {
        /// <summary>
        /// HTTP method for the operation
        /// </summary>
        public HttpMethod Method { get; set; } = HttpMethod.Get;

        /// <summary>
        /// Relative URL for the operation
        /// </summary>
        public string Url { get; set; } = string.Empty;

        /// <summary>
        /// Request body (for POST/PATCH operations)
        /// </summary>
        public object? Body { get; set; }

        /// <summary>
        /// Type of operation being performed
        /// </summary>
        public BatchOperationType OperationType { get; set; }

        /// <summary>
        /// ID of the item being operated on
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Request headers for the operation
        /// </summary>
        public Dictionary<string, string>? Headers { get; set; }

        /// <summary>
        /// Unique identifier for this batch operation
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();
    }

    /// <summary>
    /// Types of operations that can be performed in a batch
    /// </summary>
    public enum BatchOperationType
    {
        /// <summary>
        /// Create a new item
        /// </summary>
        Create,

        /// <summary>
        /// Update an existing item
        /// </summary>
        Update,

        /// <summary>
        /// Delete an existing item
        /// </summary>
        Delete,

        /// <summary>
        /// Get/retrieve an item
        /// </summary>
        Get
    }

    /// <summary>
    /// Results of batch operations
    /// </summary>
    public class BatchOperationResults
    {
        /// <summary>
        /// List of individual operation results
        /// </summary>
        public List<BatchOperationResult> Results { get; set; } = new List<BatchOperationResult>();

        /// <summary>
        /// Successfully completed operations
        /// </summary>
        public IEnumerable<BatchOperationResult> SuccessfulResults => Results.Where(r => r.IsSuccessful);

        /// <summary>
        /// Failed operations
        /// </summary>
        public IEnumerable<BatchOperationResult> FailedResults => Results.Where(r => !r.IsSuccessful);

        /// <summary>
        /// Total number of operations
        /// </summary>
        public int TotalCount => Results.Count;

        /// <summary>
        /// Number of successful operations
        /// </summary>
        public int SuccessCount => SuccessfulResults.Count();

        /// <summary>
        /// Number of failed operations
        /// </summary>
        public int FailureCount => FailedResults.Count();
    }

    /// <summary>
    /// Result of a single batch operation
    /// </summary>
    public class BatchOperationResult
    {
        /// <summary>
        /// Type of operation that was performed
        /// </summary>
        public BatchOperationType OperationType { get; set; }

        /// <summary>
        /// ID of the item that was operated on
        /// </summary>
        public string ItemId { get; set; } = string.Empty;

        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool IsSuccessful { get; set; }

        /// <summary>
        /// HTTP status code returned by the operation
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// Response body from the operation
        /// </summary>
        public string Response { get; set; } = string.Empty;

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Unique identifier for the batch operation
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// Timestamp when the operation was completed
        /// </summary>
        public DateTimeOffset CompletedAt { get; set; } = DateTimeOffset.UtcNow;
    }
}