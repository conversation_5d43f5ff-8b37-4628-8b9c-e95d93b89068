using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Infrastructure
{
    /// <summary>
    /// User-based batching channel for grouping and processing messages by user
    /// to optimize Graph API calls and database operations.
    /// </summary>
    public class UserBatchChannel
    {
        private readonly ILogger<UserBatchChannel> _logger;
        private readonly ApplicationOptions _options;
        
        private readonly ConcurrentDictionary<string, Channel<AppointmentMessage>> _userChannels = new();
        private readonly ConcurrentDictionary<string, Timer> _userBatchTimers = new();
        private readonly ConcurrentDictionary<string, List<AppointmentMessage>> _pendingBatches = new();
        private readonly SemaphoreSlim _batchSemaphore = new(1, 1);
        
        // Called when a batch for a specific user is ready
        public event Func<string, List<AppointmentMessage>, CancellationToken, Task> BatchReady;
        
        // Constants
        private const int DefaultBatchSize = 20;
        private const int DefaultBatchTimeoutSeconds = 5;
        
        public UserBatchChannel(
            ILogger<UserBatchChannel> logger,
            IOptions<ApplicationOptions> options)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        }
        
        /// <summary>
        /// Writes a message to the user's channel, batching by userId+tenantId
        /// </summary>
        public async Task WriteAsync(AppointmentMessage message, CancellationToken cancellationToken)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));
            
            // Use userId+tenantId as the key for batching
            string userKey = $"{message.UserId}_{message.TenantId}";
            
            // Get or create a channel for this user
            var userChannel = _userChannels.GetOrAdd(userKey, _ => CreateUserChannel());
            
            // Add or update a timer for this user's batch
            EnsureBatchTimer(userKey);
            
            // Write to the user's channel
            await userChannel.Writer.WriteAsync(message, cancellationToken);
            
            _logger.LogTrace("Added message for user {UserKey} to batch channel", userKey);
        }
        
        /// <summary>
        /// Start processing messages for all users
        /// </summary>
        public async Task StartProcessingAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting user batch channel processing");
            
            // Create a task for each user's channel to process messages
            var processingTasks = _userChannels.Select(pair => 
                ProcessUserChannelAsync(pair.Key, pair.Value, cancellationToken)).ToList();
            
            // Wait for all processing tasks to complete
            await Task.WhenAll(processingTasks);
            
            _logger.LogInformation("User batch channel processing completed");
        }
        
        /// <summary>
        /// Ensures a timer exists for triggering batch processing for a user after timeout
        /// </summary>
        private void EnsureBatchTimer(string userKey)
        {
            _userBatchTimers.GetOrAdd(userKey, key => 
            {
                var batchTimeout = TimeSpan.FromSeconds(_options.BatchTimeoutSeconds ?? DefaultBatchTimeoutSeconds);
                return new Timer(async _ => await TriggerBatchProcessingAsync(key), 
                    null, batchTimeout, batchTimeout);
            });
        }
        
        /// <summary>
        /// Creates a channel for a specific user
        /// </summary>
        private Channel<AppointmentMessage> CreateUserChannel()
        {
            return Channel.CreateBounded<AppointmentMessage>(new BoundedChannelOptions(_options.BatchSize ?? DefaultBatchSize)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleReader = true,
                SingleWriter = false
            });
        }
        
        /// <summary>
        /// Processes messages from a user's channel, batching them
        /// </summary>
        private async Task ProcessUserChannelAsync(
            string userKey, 
            Channel<AppointmentMessage> channel, 
            CancellationToken cancellationToken)
        {
            var batch = new List<AppointmentMessage>();
            var batchSize = _options.BatchSize ?? DefaultBatchSize;
            
            try
            {
                while (!cancellationToken.IsCancellationRequested && !channel.Reader.Completion.IsCompleted)
                {
                    try
                    {
                        // Try to read a message
                        if (await channel.Reader.WaitToReadAsync(cancellationToken))
                        {
                            if (channel.Reader.TryRead(out var message))
                            {
                                // Add message to batch
                                batch.Add(message);
                                
                                // If batch is full, process it
                                if (batch.Count >= batchSize)
                                {
                                    await ProcessBatchAsync(userKey, batch, cancellationToken);
                                    batch = new List<AppointmentMessage>();
                                }
                            }
                        }
                    }
                    catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                    {
                        // Normal cancellation
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing message for user {UserKey}", userKey);
                        // Continue processing other messages
                    }
                }
                
                // Process any remaining messages in the batch
                if (batch.Count > 0)
                {
                    await ProcessBatchAsync(userKey, batch, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in user channel processor for user {UserKey}", userKey);
            }
        }
        
        /// <summary>
        /// Triggers batch processing for a user based on timeout
        /// </summary>
        private async Task TriggerBatchProcessingAsync(string userKey)
        {
            try
            {
                // Ensure we have a batch for this user
                var userBatch = _pendingBatches.GetOrAdd(userKey, _ => new List<AppointmentMessage>());
                
                // If there are messages in the pending batch, process them
                if (userBatch.Count > 0)
                {
                    await _batchSemaphore.WaitAsync();
                    try
                    {
                        // Double-check after acquiring semaphore
                        userBatch = _pendingBatches[userKey];
                        if (userBatch.Count > 0)
                        {
                            // Get a copy of the batch and clear the original
                            var batchToProcess = userBatch.ToList();
                            userBatch.Clear();
                            
                            // Trigger the batch processing
                            if (BatchReady != null)
                            {
                                await BatchReady.Invoke(userKey, batchToProcess, CancellationToken.None);
                            }
                            
                            _logger.LogInformation("Triggered batch processing for user {UserKey} with {Count} messages due to timeout", 
                                userKey, batchToProcess.Count);
                        }
                    }
                    finally
                    {
                        _batchSemaphore.Release();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering batch processing for user {UserKey}", userKey);
            }
        }
        
        /// <summary>
        /// Processes a batch of messages for a user
        /// </summary>
        private async Task ProcessBatchAsync(string userKey, List<AppointmentMessage> batch, CancellationToken cancellationToken)
        {
            if (batch == null || batch.Count == 0) return;
            
            try
            {
                _logger.LogDebug("Processing batch of {Count} messages for user {UserKey}", batch.Count, userKey);
                
                // Raise the batch ready event
                if (BatchReady != null)
                {
                    await BatchReady.Invoke(userKey, batch, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing batch for user {UserKey}", userKey);
                // Don't rethrow - we want to continue processing other batches
            }
        }
        
        /// <summary>
        /// Performs cleanup when shutting down
        /// </summary>
        public async Task ShutdownAsync()
        {
            _logger.LogInformation("Shutting down user batch channel");
            
            // Dispose all timers
            foreach (var timer in _userBatchTimers.Values)
            {
                timer.Dispose();
            }
            
            // Process any remaining batches
            foreach (var batch in _pendingBatches)
            {
                if (batch.Value.Count > 0)
                {
                    try
                    {
                        if (BatchReady != null)
                        {
                            await BatchReady.Invoke(batch.Key, batch.Value, CancellationToken.None);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing final batch for user {UserKey}", batch.Key);
                    }
                }
            }
            
            // Complete all channels
            foreach (var channel in _userChannels.Values)
            {
                channel.Writer.Complete();
            }
            
            _logger.LogInformation("User batch channel shutdown complete");
        }
    }
}
