using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Confluent.Kafka;
using Confluent.SchemaRegistry;
using Confluent.SchemaRegistry.Serdes;
using Microsoft.Extensions.Logging;
using Polly;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Serialization
{
    public interface IAvroMessageDeserializer
    {
        Task<AppointmentMessage> DeserializeAsync(byte[] data, CancellationToken cancellationToken = default);
        Task<MessageKey> DeserializeKeyAsync(byte[] data, CancellationToken cancellationToken = default);
    }

    public class AvroMessageDeserializer : IAvroMessageDeserializer
    {
        private readonly ILogger<AvroMessageDeserializer> _logger;
        private readonly ISchemaRegistryClient _schemaRegistryClient;
        private readonly ResiliencePolicies _resilience;
        
        private readonly Dictionary<int, Avro.Schema> _schemaCache = new();

        public AvroMessageDeserializer(
            ILogger<AvroMessageDeserializer> logger,
            ISchemaRegistryClient schemaRegistryClient,
            ResiliencePolicies resilience)
        {
            _logger = logger;
            _schemaRegistryClient = schemaRegistryClient;
            _resilience = resilience;
        }

        public async Task<AppointmentMessage> DeserializeAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null || data.Length < 5)
            {
                throw new DeserializationException("Message data is null or too short");
            }

            try
            {
                return await _resilience.ConsumerPolicy.ExecuteAsync(async (ctx) =>
                {
                    var deserializer = new AvroDeserializer<AppointmentMessage>(_schemaRegistryClient);
                    var serializationContext = new SerializationContext(MessageComponentType.Value, "calendar-events");
                    return await deserializer.DeserializeAsync(data, false, serializationContext, cancellationToken);
                },
                new Context("DeserializeMessage"));
            }
            catch (Exception ex) when (!(ex is DeserializationException))
            {
                _logger.LogError(ex, "Error deserializing Avro message");
                throw new DeserializationException("Failed to deserialize Avro message", ex);
            }
        }

        public async Task<MessageKey> DeserializeKeyAsync(byte[] data, CancellationToken cancellationToken = default)
        {
            if (data == null || data.Length < 5)
            {
                throw new DeserializationException("Message key data is null or too short");
            }

            try
            {
                return await _resilience.ConsumerPolicy.ExecuteAsync(async (ctx) =>
                {
                    var deserializer = new AvroDeserializer<MessageKey>(_schemaRegistryClient);
                    var serializationContext = new SerializationContext(MessageComponentType.Key, "calendar-events");
                    return await deserializer.DeserializeAsync(data, false, serializationContext, cancellationToken);
                },
                new Context("DeserializeKey"));
            }
            catch (Exception ex) when (!(ex is DeserializationException))
            {
                _logger.LogError(ex, "Error deserializing Avro message key");
                throw new DeserializationException("Failed to deserialize Avro message key", ex);
            }
        }
    }
}
