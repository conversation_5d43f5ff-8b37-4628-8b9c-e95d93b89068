using System;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Polly;
using System.Linq;
using AppOptions = Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration.ApplicationOptions;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Service responsible for managing authentication tokens for Microsoft Graph API access
    /// with advanced token caching, refresh strategies and resilient token acquisition.
    /// </summary>
    public class TokenManagementService
    {
        private readonly ILogger<TokenManagementService> _logger;
        private readonly AppOptions _appOptions;
        private readonly MicrosoftGraphApiOptions _graphOptions;
        private readonly IConfidentialClientApplication _clientApplication;
        private readonly ConcurrentDictionary<string, (string Token, DateTimeOffset ExpiresOn)> _tokenCache = new();
        private readonly AsyncPolicy _tokenAcquisitionPolicy;
        private readonly SemaphoreSlim _tokenRefreshSemaphore = new SemaphoreSlim(1, 1);

        private const int TokenRefreshThresholdMinutes = 5;
        private const string GraphApiScope = "https://graph.microsoft.com/.default";

        public TokenManagementService(
            ILogger<TokenManagementService> logger,
            IOptions<AppOptions> appOptions,
            IOptions<MicrosoftGraphApiOptions> graphOptions)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _appOptions = appOptions?.Value ?? throw new ArgumentNullException(nameof(appOptions));
            _graphOptions = graphOptions?.Value ?? throw new ArgumentNullException(nameof(graphOptions));

            // Create the MSAL confidential client application
            _clientApplication = ConfidentialClientApplicationBuilder
                .Create(_graphOptions.ClientId)
                .WithClientSecret(_graphOptions.ClientSecret)
                .WithAuthority(new Uri($"{_graphOptions.Login}/{_graphOptions.TenantId}"))
                .Build();

            // Set up resilience policy for token acquisition
            _tokenAcquisitionPolicy = ResiliencePolicies.CreateTokenAcquisitionPolicy(logger);
        }

        /// <summary>
        /// Gets a valid access token for the specified user, refreshing or acquiring new tokens as necessary
        /// </summary>
        /// <param name="userConfig">The user configuration containing refresh token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A valid access token</returns>
        /// <exception cref="TokenAcquisitionException">Thrown when token acquisition fails after retries</exception>
        public async Task<string> GetAccessTokenAsync(UserSyncConfig userConfig, CancellationToken cancellationToken)
        {
            if (userConfig == null)
                throw new ArgumentNullException(nameof(userConfig));

            string cacheKey = $"{userConfig.UserId}_{userConfig.FirmId}";

            // Check if we have a valid cached token
            if (_tokenCache.TryGetValue(cacheKey, out var cachedToken))
            {
                // If token is not close to expiration, return it
                if (cachedToken.ExpiresOn > DateTimeOffset.UtcNow.AddMinutes(TokenRefreshThresholdMinutes))
                {
                    _logger.LogDebug("Using cached token for user {UserId}", userConfig.UserId);
                    return cachedToken.Token;
                }

                _logger.LogDebug("Token for user {UserId} is expiring soon, refreshing", userConfig.UserId);
            }

            try
            {
                // Ensure only one refresh happens at a time for the same user
                await _tokenRefreshSemaphore.WaitAsync(cancellationToken);

                try
                {
                    // Check cache again in case another thread refreshed the token while we were waiting
                    if (_tokenCache.TryGetValue(cacheKey, out cachedToken) &&
                        cachedToken.ExpiresOn > DateTimeOffset.UtcNow.AddMinutes(TokenRefreshThresholdMinutes))
                    {
                        return cachedToken.Token;
                    }

                    // Acquire a new token using the resilience policy
                    return await _tokenAcquisitionPolicy.ExecuteAsync(async () =>
                    {
                        // Use client credentials flow for application-only access
                        try
                        {
                            var result = await _clientApplication.AcquireTokenForClient(new[] { GraphApiScope })
                                .ExecuteAsync(cancellationToken);

                            // Cache the new token
                            _tokenCache[cacheKey] = (result.AccessToken, result.ExpiresOn);

                            _logger.LogInformation("Acquired application token for user {UserId}", userConfig.UserId);
                            return result.AccessToken;
                        }
                        catch (MsalException ex)
                        {
                            _logger.LogError(ex, "Failed to acquire application token: {ErrorCode}", ex.ErrorCode);
                            throw new TokenAcquisitionException(
                                "Failed to acquire application token", ex, isTransient: IsTransientMsalError(ex));
                        }
                    });
                }
                finally
                {
                    _tokenRefreshSemaphore.Release();
                }
            }
            catch (Exception ex) when (ex is not TokenAcquisitionException)
            {
                _logger.LogError(ex, "Unexpected error acquiring token for user {UserId}", userConfig.UserId);
                throw new TokenAcquisitionException(
                    $"Unexpected error acquiring token for user {userConfig.UserId}", ex, isTransient: true);
            }
        }

        /// <summary>
        /// Invalidates a cached token for a specific user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="firmId">The firm ID</param>
        public void InvalidateToken(int userId, int firmId)
        {
            string cacheKey = $"{userId}_{firmId}";
            _tokenCache.TryRemove(cacheKey, out _);
            _logger.LogInformation("Invalidated token for user {UserId}", userId);
        }

        /// <summary>
        /// Determines if a MSAL error is transient and can be retried
        /// </summary>
        private bool IsTransientMsalError(MsalException exception)
        {
            // List of MSAL error codes that are considered transient
            string[] transientErrorCodes = new[]
            {
                "temporarily_unavailable",
                "server_error",
                "service_not_available",
                "request_timeout",
                "gateway_timeout"
            };

            return transientErrorCodes.Contains(exception.ErrorCode) ||
                   exception.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) ||
                   exception.Message.Contains("temporarily", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets the global application token for operations not specific to a user
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>A valid application access token</returns>
        public Task<string> GetApplicationTokenAsync(CancellationToken cancellationToken)
        {
            // Create a dummy user config for application-wide operations
            var appUserConfig = new UserSyncConfig
            {
                UserId = 0, // Use 0 for application-wide operations
                FirmId = int.TryParse(_graphOptions.TenantId, out var tenantId) ? tenantId : 0
            };

            return GetAccessTokenAsync(appUserConfig, cancellationToken);
        }
    }
}
