using System;
using Confluent.Kafka;
using Confluent.Kafka.SyncOverAsync;
using Confluent.SchemaRegistry;
using Confluent.SchemaRegistry.Serdes;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Avro.Generic;
using Avro.Specific;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    public class KafkaConsumerFactory
    {
        private readonly ILogger<KafkaConsumerFactory> _logger;
        private readonly KafkaOptions _kafkaOptions;

        public KafkaConsumerFactory(
            ILogger<KafkaConsumerFactory> logger,
            IOptions<KafkaOptions> kafkaOptions)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _kafkaOptions = kafkaOptions?.Value ?? throw new ArgumentNullException(nameof(kafkaOptions));
        }
        
        public IConsumer<string, byte[]> CreateBinaryConsumer()
        {
            _logger.LogInformation("Creating binary Kafka consumer for group {GroupId}", _kafkaOptions.ConsumerGroupId);

            var config = CreateBaseConsumerConfig();
            
            return new ConsumerBuilder<string, byte[]>(config)
                .SetErrorHandler((_, error) => LogConsumerError(error))
                .SetStatisticsHandler((_, stats) => LogConsumerStatistics(stats))
                .SetPartitionsAssignedHandler((consumer, partitions) => 
                    _logger.LogInformation("Partitions assigned: {Partitions}", string.Join(", ", partitions)))
                .SetPartitionsRevokedHandler((consumer, partitions) => 
                    _logger.LogInformation("Partitions revoked: {Partitions}", string.Join(", ", partitions)))
                .SetOffsetsCommittedHandler((consumer, offsets) => 
                    _logger.LogDebug("Offsets committed: {Offsets}", string.Join(", ", offsets.Offsets)))
                .Build();
        }

        public IConsumer<TKey, TValue> CreateSpecificAvroConsumer<TKey, TValue>()
            where TKey : class, ISpecificRecord
            where TValue : class, ISpecificRecord
        {
            _logger.LogInformation("Creating Specific Avro Kafka consumer for group {GroupId}, key type {KeyType}, value type {ValueType}", 
                _kafkaOptions.ConsumerGroupId, typeof(TKey).Name, typeof(TValue).Name);

            var config = CreateBaseConsumerConfig();
            var schemaRegistry = CreateSchemaRegistryClient();

            return new ConsumerBuilder<TKey, TValue>(config)
                .SetErrorHandler((_, error) => LogConsumerError(error))
                .SetStatisticsHandler((_, stats) => LogConsumerStatistics(stats))
                .SetKeyDeserializer(new AvroDeserializer<TKey>(schemaRegistry).AsSyncOverAsync())
                .SetValueDeserializer(new AvroDeserializer<TValue>(schemaRegistry).AsSyncOverAsync())
                .SetPartitionsAssignedHandler((consumer, partitions) => 
                    _logger.LogInformation("Partitions assigned: {Partitions}", string.Join(", ", partitions)))
                .SetPartitionsRevokedHandler((consumer, partitions) => 
                    _logger.LogInformation("Partitions revoked: {Partitions}", string.Join(", ", partitions)))
                .SetOffsetsCommittedHandler((consumer, offsets) => 
                    _logger.LogDebug("Offsets committed: {Offsets}", string.Join(", ", offsets.Offsets)))
                .Build();
        }

        public IConsumer<string, T> CreateAvroConsumer<T>() where T : class, ISpecificRecord
        {
            _logger.LogInformation("Creating Avro Kafka consumer for group {GroupId}, type {MessageType}", 
                _kafkaOptions.ConsumerGroupId, typeof(T).Name);

            var config = CreateBaseConsumerConfig();
            var schemaRegistry = CreateSchemaRegistryClient();

            return new ConsumerBuilder<string, T>(config)
                .SetErrorHandler((_, error) => LogConsumerError(error))
                .SetStatisticsHandler((_, stats) => LogConsumerStatistics(stats))
                .SetValueDeserializer(new AvroDeserializer<T>(schemaRegistry).AsSyncOverAsync())
                .SetPartitionsAssignedHandler((consumer, partitions) => 
                    _logger.LogInformation("Partitions assigned: {Partitions}", string.Join(", ", partitions)))
                .SetPartitionsRevokedHandler((consumer, partitions) => 
                    _logger.LogInformation("Partitions revoked: {Partitions}", string.Join(", ", partitions)))
                .SetOffsetsCommittedHandler((consumer, offsets) => 
                    _logger.LogDebug("Offsets committed: {Offsets}", string.Join(", ", offsets.Offsets)))
                .Build();
        }

        public IConsumer<string, GenericRecord> CreateGenericAvroConsumer()
        {
            _logger.LogInformation("Creating Generic Avro Kafka consumer for group {GroupId}", _kafkaOptions.ConsumerGroupId);

            var config = CreateBaseConsumerConfig();
            var schemaRegistry = CreateSchemaRegistryClient();

            return new ConsumerBuilder<string, GenericRecord>(config)
                .SetErrorHandler((_, error) => LogConsumerError(error))
                .SetStatisticsHandler((_, stats) => LogConsumerStatistics(stats))
                .SetValueDeserializer(new AvroDeserializer<GenericRecord>(schemaRegistry).AsSyncOverAsync())
                .SetPartitionsAssignedHandler((consumer, partitions) => 
                    _logger.LogInformation("Partitions assigned: {Partitions}", string.Join(", ", partitions)))
                .SetPartitionsRevokedHandler((consumer, partitions) => 
                    _logger.LogInformation("Partitions revoked: {Partitions}", string.Join(", ", partitions)))
                .SetOffsetsCommittedHandler((consumer, offsets) => 
                    _logger.LogDebug("Offsets committed: {Offsets}", string.Join(", ", offsets.Offsets)))
                .Build();
        }

        private ISchemaRegistryClient CreateSchemaRegistryClient()
        {
            var schemaRegistryConfig = new SchemaRegistryConfig
            {
                Url = _kafkaOptions.SchemaRegistryUrl
            };

            if (!string.IsNullOrEmpty(_kafkaOptions.SaslUsername))
            {
                schemaRegistryConfig.BasicAuthCredentialsSource = AuthCredentialsSource.UserInfo;
                schemaRegistryConfig.BasicAuthUserInfo = $"{_kafkaOptions.SaslUsername}:{_kafkaOptions.SaslPassword}";
            }

            return new CachedSchemaRegistryClient(schemaRegistryConfig);
        }

        private ConsumerConfig CreateBaseConsumerConfig()
        {
            var config = new ConsumerConfig
            {
                BootstrapServers = _kafkaOptions.BootstrapServers,
                GroupId = _kafkaOptions.ConsumerGroupId,
                AutoOffsetReset = Enum.TryParse<AutoOffsetReset>(_kafkaOptions.AutoOffsetReset, true, out var offsetReset)
                    ? offsetReset : AutoOffsetReset.Earliest,
                EnableAutoCommit = _kafkaOptions.AutoCommitEnabled,
                EnableAutoOffsetStore = false,
                MaxPollIntervalMs = _kafkaOptions.MaxPollIntervalMs,
                SessionTimeoutMs = _kafkaOptions.SessionTimeoutMs,  
                HeartbeatIntervalMs = _kafkaOptions.HeartbeatIntervalMs,
                FetchMaxBytes = _kafkaOptions.FetchMaxBytes,
                MaxPartitionFetchBytes = _kafkaOptions.MaxPartitionFetchBytes,
                AutoCommitIntervalMs = _kafkaOptions.AutoCommitIntervalMs
            };

            if (!string.IsNullOrEmpty(_kafkaOptions.SaslUsername) && !string.IsNullOrEmpty(_kafkaOptions.SaslPassword))
            {
                config.SecurityProtocol = SecurityProtocol.SaslSsl;
                config.SaslMechanism = SaslMechanism.Plain;
                config.SaslUsername = _kafkaOptions.SaslUsername;
                config.SaslPassword = _kafkaOptions.SaslPassword;
            }
            
            return config;
        }
        
        private void LogConsumerError(Error error)
        {
            if (error.IsFatal)
            {
                _logger.LogError("Fatal Kafka consumer error: {ErrorCode} - {Reason}", error.Code, error.Reason);
            }
            else
            {
                _logger.LogWarning("Kafka consumer error: {ErrorCode} - {Reason}", error.Code, error.Reason);
            }
        }
        
        private void LogConsumerStatistics(string statistics)
        {
            _logger.LogDebug("Kafka consumer statistics: {Statistics}", statistics);
        }
    }
}
