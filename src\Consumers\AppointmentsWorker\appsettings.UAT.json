{"Application": {"EnableDetailedLogs": true, "RetryMaxAttempts": 6, "RetryDelayInSeconds": 2, "CircuitBreakerThreshold": 6, "CircuitBreakerDurationInSeconds": 90, "TimeoutInSeconds": 45, "DeadLetterQueueEnabled": true}, "Kafka": {"BootstrapServers": "kafka-f68bd74-caretlegal-data-bus.b.aivencloud.com:15904", "UseSecureConnection": true, "SaslUsername": "a<PERSON><PERSON><PERSON><PERSON>", "SaslPassword": "AVNS_Boi1zX2ngjmFsqmsd1s", "MessageTimeoutMs": 450000, "BatchSize": 150}, "Database": {"SystemConnectionString": "Data Source=zolauatdb.amicusuat.amicuscreative.com;Initial Catalog=zola-uat;Trusted_Connection=True;max pool size=750;TrustServerCertificate=True", "MaxRetries": 6, "RetryDelaySeconds": 2, "CommandTimeoutSeconds": 45, "EnableSensitiveDataLogging": false, "ConnectionPooling": {"MinPoolSize": 8, "MaxPoolSize": 750, "ConnectionLifetimeSeconds": 450}}, "MicrosoftGraphApi": {"Login": "https://login.microsoftonline.com", "GraphUrlBase": "https://graph.microsoft.com/v1.0", "ClientId": "151b6300-7b83-4530-9838-7ca8f25d2efd", "ClientSecret": "**********************************", "GraphScopes": ["https://graph.microsoft.com/.default"], "GraphRedirectUrl": "https://uat.zola.com/Settings/UserSettings/OutlookFolderMapping.aspx", "RedirectUrl": "https://uat.zola.com/calendar/sync/outlookredirecturlcalendarsync.aspx", "TenantId": "common"}, "Resilience": {"GraphApi": {"RetryCount": 4, "RetryDelayInSeconds": 2, "CircuitBreakerThreshold": 8, "CircuitBreakerDurationInSeconds": 90}, "Database": {"RetryCount": 6, "RetryDelayInSeconds": 2, "CircuitBreakerThreshold": 12, "CircuitBreakerDurationInSeconds": 150}, "TokenAcquisition": {"RetryCount": 4, "RetryDelayInSeconds": 1, "CircuitBreakerThreshold": 8, "CircuitBreakerDurationInSeconds": 450}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}, "Datadog": {"Enabled": true, "Environment": "UAT", "Service": "AppointmentsConsumer", "Tags": ["env:uat", "project:caretlegal", "component:appointments"]}}