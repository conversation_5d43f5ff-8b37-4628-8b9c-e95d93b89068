using System;
using FluentValidation;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Validation
{
    /// <summary>
    /// Validator for AppointmentMessage objects
    /// </summary>
    public class AppointmentMessageValidator : AbstractValidator<AppointmentMessage>
    {
        public AppointmentMessageValidator()
        {
            // Basic properties validation
            RuleFor(x => x.CorrelationId)
                .NotEmpty().WithMessage("CorrelationId is required")
                .Must(x => Guid.TryParse(x, out _)).WithMessage("CorrelationId must be a valid GUID");

            RuleFor(x => x.Operation)
                .NotEmpty().WithMessage("Operation is required")
                .Must(BeValidOperation).WithMessage("Operation must be one of: i (insert), u (update), d (delete)");

            // Subject validation
            RuleFor(x => x.Subject)
                .NotEmpty().WithMessage("Subject is required")
                .MaximumLength(255).WithMessage("Subject cannot exceed 255 characters");

            // Attendees validation (only if present)
            When(x => x.Attendees != null && x.Attendees.Count > 0, () =>
            {
                RuleForEach(x => x.Attendees)
                    .SetValidator(new MessageAttendeeValidator());
            });

            // Data validation (only if present)
            When(x => x.Data != null, () =>
            {
                RuleFor(x => x.Data)
                    .SetValidator(new AppointmentDataValidator());
            });
        }

        private bool BeValidOperation(string? operation)
        {
            if (string.IsNullOrEmpty(operation))
                return false;

            return operation == "i" || operation == "u" || operation == "d";
        }
    }

    /// <summary>
    /// Validator for MessageAttendee objects
    /// </summary>
    public class MessageAttendeeValidator : AbstractValidator<MessageAttendee>
    {
        public MessageAttendeeValidator()
        {
            RuleFor(x => x.Email)
                .NotEmpty().WithMessage("Attendee email is required")
                .EmailAddress().WithMessage("Attendee email must be a valid email address");

            RuleFor(x => x.Type)
                .Must(BeValidAttendeeType).WithMessage("Attendee type must be one of the valid types (Required, Optional, etc.)");
        }

        private bool BeValidAttendeeType(string? type)
        {
            // Assuming valid types are "Required", "Optional", etc.
            if (string.IsNullOrEmpty(type))
                return false;

            var validTypes = new[] { "Required", "Optional", "Resource" };
            return Array.Exists(validTypes, t => t.Equals(type, StringComparison.OrdinalIgnoreCase));
        }
    }

    /// <summary>
    /// Validator for AppointmentData objects
    /// </summary>
    public class AppointmentDataValidator : AbstractValidator<AppointmentData>
    {
        public AppointmentDataValidator()
        {
            // Implement validation rules based on AppointmentData structure
            // Example:
            RuleFor(x => x.Id)
                .NotEmpty().WithMessage("Data ID is required");
        }
    }
}
