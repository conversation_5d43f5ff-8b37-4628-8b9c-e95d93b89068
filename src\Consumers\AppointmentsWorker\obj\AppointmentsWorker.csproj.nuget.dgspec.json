{"format": 1, "restore": {"C:\\Users\\<USER>\\Code\\Caret.CaretLegal.AppointmentsConsumer\\src\\Consumers\\AppointmentsWorker\\AppointmentsWorker.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Code\\Caret.CaretLegal.AppointmentsConsumer\\src\\Consumers\\AppointmentsWorker\\AppointmentsWorker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Code\\Caret.CaretLegal.AppointmentsConsumer\\src\\Consumers\\AppointmentsWorker\\AppointmentsWorker.csproj", "projectName": "AppointmentsWorker", "projectPath": "C:\\Users\\<USER>\\Code\\Caret.CaretLegal.AppointmentsConsumer\\src\\Consumers\\AppointmentsWorker\\AppointmentsWorker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Code\\Caret.CaretLegal.AppointmentsConsumer\\src\\Consumers\\AppointmentsWorker\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Apache.Avro": {"target": "Package", "version": "[1.11.3, )"}, "Confluent.Kafka": {"target": "Package", "version": "[2.3.0, )"}, "Confluent.SchemaRegistry": {"target": "Package", "version": "[2.3.0, )"}, "Confluent.SchemaRegistry.Serdes.Avro": {"target": "Package", "version": "[2.3.0, )"}, "Dapper": {"target": "Package", "version": "[2.1.28, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.9.0, )"}, "Microsoft.ApplicationInsights.WorkerService": {"target": "Package", "version": "[2.21.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[5.1.5, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[7.0.17, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.17, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[7.0.1, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.DataAnnotations": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Graph": {"target": "Package", "version": "[5.43.0, )"}, "Microsoft.Graph.Core": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.Identity.Client": {"target": "Package", "version": "[4.59.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "OpenTelemetry": {"target": "Package", "version": "[1.7.0, )"}, "OpenTelemetry.Api": {"target": "Package", "version": "[1.7.0, )"}, "OpenTelemetry.Exporter.Console": {"target": "Package", "version": "[1.7.0, )"}, "OpenTelemetry.Exporter.OpenTelemetryProtocol": {"target": "Package", "version": "[1.7.0, )"}, "OpenTelemetry.Extensions.Hosting": {"target": "Package", "version": "[1.7.0, )"}, "OpenTelemetry.Instrumentation.Http": {"target": "Package", "version": "[1.7.0, )"}, "OpenTelemetry.Instrumentation.SqlClient": {"target": "Package", "version": "[1.6.0-beta.3, )"}, "Polly": {"target": "Package", "version": "[8.2.1, )"}, "Polly.Contrib.WaitAndRetry": {"target": "Package", "version": "[1.1.1, )"}, "Serilog.Extensions.Hosting": {"target": "Package", "version": "[7.0.0, )"}, "Serilog.Formatting.Compact": {"target": "Package", "version": "[2.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[5.0.1, )"}, "Serilog.Sinks.Datadog.Logs": {"target": "Package", "version": "[0.5.2, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.6, )"}, "System.Linq.Async": {"target": "Package", "version": "[6.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[7.0.20, 7.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}