using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Gateways;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using System.Net.Http;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Processes calendar events in batches for optimal performance and API usage
    /// with comprehensive error handling and telemetry.
    /// </summary>
    public class BatchProcessor
    {
        private readonly ILogger<BatchProcessor> _logger;
        private readonly GraphApiService _graphApiService;
        private readonly DatabaseGateway _databaseGateway;
        private readonly ApplicationOptions _options;
        private readonly UserBatchChannel _userBatchChannel;
        private readonly SemaphoreSlim _batchSemaphore;
        
        // Batch size constraints
        private const int OptimalBatchSize = 10;
        private const int MaxBatchSize = 20;  // Microsoft Graph API limit
        
        public BatchProcessor(
            ILogger<BatchProcessor> logger,
            GraphApiService graphApiService,
            DatabaseGateway databaseGateway,
            IOptions<ApplicationOptions> options,
            UserBatchChannel userBatchChannel)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphApiService = graphApiService ?? throw new ArgumentNullException(nameof(graphApiService));
            _databaseGateway = databaseGateway ?? throw new ArgumentNullException(nameof(databaseGateway));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _userBatchChannel = userBatchChannel ?? throw new ArgumentNullException(nameof(userBatchChannel));
            
            // Initialize semaphore for controlling concurrent batch processing
            _batchSemaphore = new SemaphoreSlim(_options.MaxConcurrentBatches, _options.MaxConcurrentBatches);
            
            // Subscribe to user batch events
            _userBatchChannel.BatchReady += ProcessUserBatchAsync;
        }        /// <summary>
        /// Processes a batch of appointment messages, grouping by user for optimal API usage
        /// </summary>
        /// <param name="messages">List of appointment messages to process</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Processing results with success/failure counts</returns>
        public async Task<BatchProcessingResult> ProcessAppointmentBatchAsync(
            List<AppointmentMessage> messages,
            CancellationToken cancellationToken)
        {
            if (messages == null || !messages.Any())
            {
                return new BatchProcessingResult { TotalMessages = 0 };
            }
            
            var result = new BatchProcessingResult
            {
                TotalMessages = messages.Count,
                StartedAt = DateTimeOffset.UtcNow
            };
            
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                if (_options.EnableUserBatching)
                {
                    // Use user-based batching for optimal performance
                    await ProcessWithUserBatchChannelAsync(messages, result, cancellationToken);
                }
                else
                {
                    // Fall back to simple user grouping
                    await ProcessWithSimpleGroupingAsync(messages, result, cancellationToken);
                }
            }
            finally
            {
                stopwatch.Stop();
                result.CompletedAt = DateTimeOffset.UtcNow;
                result.ProcessingTime = stopwatch.Elapsed;
                
                _logger.LogInformation(
                    "Batch processing completed: {SuccessCount} successful, {FailedCount} failed, {SkippedCount} skipped, duration: {Duration}ms",
                    result.SuccessfulMessages, result.FailedMessages, result.SkippedMessages, result.ProcessingTime.TotalMilliseconds);
            }
            
            return result;
        }
        
        /// <summary>
        /// Processes messages using the UserBatchChannel for optimal batching
        /// </summary>
        private async Task ProcessWithUserBatchChannelAsync(
            List<AppointmentMessage> messages,
            BatchProcessingResult result,
            CancellationToken cancellationToken)
        {
            // Track message counts for result calculation
            var processedMessageCount = new ConcurrentDictionary<string, int>();
            var completionTasks = new List<Task>();
            
            // Create a barrier to wait for all messages to be processed
            using var messageCounter = new CountdownEvent(messages.Count);
            
            // Create a handler for message processing completion
            EventHandler<string> messageProcessed = (sender, correlationId) => {
                processedMessageCount.AddOrUpdate(correlationId, 1, (_, count) => count + 1);
                messageCounter.Signal();
            };
            
            try
            {
                // Write messages to the user batch channel
                foreach (var message in messages)
                {
                    await _userBatchChannel.WriteAsync(message, cancellationToken);
                }
                
                // Wait for all messages to be processed or timeout
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(30), cancellationToken);
                var completionTask = Task.Run(() => messageCounter.Wait(cancellationToken), cancellationToken);
                
                await Task.WhenAny(timeoutTask, completionTask);
                
                // Calculate results based on processed messages
                result.SuccessfulMessages = processedMessageCount.Count;
                result.FailedMessages = messages.Count - result.SuccessfulMessages;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing messages with user batch channel");
                result.FailedMessages = messages.Count - processedMessageCount.Count;
            }
        }
        
        /// <summary>
        /// Processes messages using simple user grouping (legacy method)
        /// </summary>
        private async Task ProcessWithSimpleGroupingAsync(
            List<AppointmentMessage> messages,
            BatchProcessingResult result,
            CancellationToken cancellationToken)
        {
            // Group messages by user for efficient processing
            var messagesByUser = messages.GroupBy(m => m.UserId);
            
            foreach (var userGroup in messagesByUser)
            {
                string userId = userGroup.Key;
                var userMessages = userGroup.ToList();
                
                _logger.LogInformation("Processing {MessageCount} messages for user {UserId}", 
                    userMessages.Count, userId);
                
                try
                {
                    // Get user sync configuration
                    var userConfig = await _databaseGateway.GetUserSyncConfigAsync(userId, cancellationToken);
                    
                    if (userConfig == null)
                    {
                        _logger.LogWarning("No sync configuration found for user {UserId}, skipping {MessageCount} messages", 
                            userId, userMessages.Count);
                        
                        result.FailedUsers.Add(userId);
                        result.SkippedMessages += userMessages.Count;
                        continue;
                    }

                    // Process all messages for this user
                    var userResult = await ProcessUserBatchLegacyAsync(userConfig, userMessages, cancellationToken);
                    
                    // Aggregate results
                    result.SuccessfulMessages += userResult.SuccessfulOperations;
                    result.FailedMessages += userResult.FailedOperations;
                    
                    if (userResult.FailedOperations > 0)
                    {
                        result.PartiallyFailedUsers.Add(userId);
                    }
                    else
                    {
                        result.SuccessfulUsers.Add(userId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process batch for user {UserId}", userId);
                    result.FailedUsers.Add(userId);
                    result.FailedMessages += userMessages.Count;
                }
            }
        }        /// <summary>
        /// Processes all appointment messages for a specific user (legacy method)
        /// </summary>
        /// <param name="userConfig">User configuration with token information</param>
        /// <param name="messages">List of appointment messages to process</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>User-specific processing results</returns>
        private async Task<UserBatchResult> ProcessUserBatchLegacyAsync(
            UserSyncConfig userConfig,
            List<AppointmentMessage> messages,
            CancellationToken cancellationToken)
        {
            var result = new UserBatchResult
            {
                UserId = userConfig.UserId,
                TotalOperations = messages.Count
            };
            
            try
            {
                // Convert messages to calendar events and batch operations
                var batchOperations = new List<BatchOperation>();
                var eventByCorrId = new Dictionary<string, CalendarEvent>();
                
                foreach (var message in messages)
                {
                    try
                    {
                        // Convert message to event and create batch operation
                        var (calendarEvent, operation) = ConvertToOperation(message, userConfig.UserId);
                        
                        if (operation != null)
                        {
                            batchOperations.Add(operation);
                            eventByCorrId[message.CorrelationId] = calendarEvent;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to convert message {CorrelationId} for user {UserId} to operation", 
                            message.CorrelationId, userConfig.UserId);
                        result.FailedOperations++;
                    }
                }
                
                // Process batch operations in optimal chunks
                foreach (var operationChunk in batchOperations.Chunk(OptimalBatchSize))
                {
                    try
                    {
                        var chunkOperations = operationChunk.ToList();
                        var batchResult = await _graphApiService.ProcessBatchAsync(userConfig, chunkOperations, cancellationToken);
                        
                        // Process the results
                        foreach (var opResult in batchResult.Results)
                        {
                            var originalOp = chunkOperations.First(o => o.ItemId == opResult.ItemId);
                            var correlationId = originalOp.ItemId;
                            
                            if (opResult.IsSuccessful)
                            {
                                result.SuccessfulOperations++;
                                
                                if (eventByCorrId.TryGetValue(correlationId, out var calendarEvent))
                                {
                                    // Update event with success information
                                    calendarEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                                    calendarEvent.SyncStatus = SyncStatus.Synced;
                                    calendarEvent.SyncErrorDetails = null;
                                    
                                    // Extract event ID for created events
                                    if (opResult.OperationType == BatchOperationType.Create && 
                                        !string.IsNullOrEmpty(opResult.Response))
                                    {
                                        try
                                        {
                                            var response = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(opResult.Response);
                                            if (response.TryGetValue("id", out var eventId))
                                            {
                                                calendarEvent.EventId = eventId.ToString();
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            _logger.LogError(ex, "Failed to parse event ID from response for {CorrelationId}", correlationId);
                                        }
                                    }
                                    
                                    // Update the calendar event in database
                                    await _databaseGateway.UpdateCalendarEventAsync(calendarEvent, cancellationToken);
                                }
                            }
                            else
                            {
                                result.FailedOperations++;
                                
                                if (eventByCorrId.TryGetValue(correlationId, out var calendarEvent))
                                {
                                    // Update event with failure information
                                    calendarEvent.SyncStatus = SyncStatus.Failed;
                                    calendarEvent.SyncErrorDetails = $"API error: Status {opResult.StatusCode} - {opResult.Response}";
                                    calendarEvent.SyncAttempts++;
                                    
                                    await _databaseGateway.UpdateCalendarEventAsync(calendarEvent, cancellationToken);
                                }
                                
                                _logger.LogError("Operation failed for {CorrelationId}: Status {StatusCode} - {Response}", 
                                    correlationId, opResult.StatusCode, opResult.Response);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process batch chunk for user {UserId}", userConfig.UserId);
                        result.FailedOperations += operationChunk.Length;
                    }
                }
            }
            catch (TokenAcquisitionException ex)
            {
                _logger.LogError(ex, "Token acquisition failed for user {UserId}", userConfig.UserId);
                result.FailedOperations = messages.Count;
                result.SuccessfulOperations = 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error processing batch for user {UserId}", userConfig.UserId);
                result.FailedOperations = messages.Count;
                result.SuccessfulOperations = 0;
            }
            
            return result;
        }

        /// <summary>
        /// Processes a batch of messages for a single user (called from UserBatchChannel)
        /// </summary>
        /// <param name="userKey">The user key (userId_tenantId)</param>
        /// <param name="messages">List of appointment messages to process</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Processing task</returns>
        private async Task ProcessUserBatchAsync(string userKey, List<AppointmentMessage> messages, CancellationToken cancellationToken)
        {
            if (messages == null || !messages.Any())
                return;
            
            // Split the user key to get user ID and tenant ID
            var keyParts = userKey.Split('_');
            if (keyParts.Length != 2 || !int.TryParse(keyParts[0], out int userId))
            {
                _logger.LogError("Invalid user key format: {UserKey}", userKey);
                return;
            }
            
            // Wait for a semaphore slot to control concurrency
            await _batchSemaphore.WaitAsync(cancellationToken);
            
            try
            {
                _logger.LogInformation("Processing user batch of {Count} messages for user {UserId}", 
                    messages.Count, userId);
                
                var stopwatch = Stopwatch.StartNew();
                
                // Get user configuration
                var userConfig = await _databaseGateway.GetUserSyncConfigAsync(userId.ToString(), cancellationToken);
                
                if (userConfig == null)
                {
                    _logger.LogWarning("No sync configuration found for user {UserId}, skipping {MessageCount} messages", 
                        userId, messages.Count);
                    return;
                }
                
                // Convert messages to calendar events
                var calendarEvents = new List<CalendarEvent>();
                
                foreach (var message in messages)
                {
                    try
                    {
                        var calendarEvent = ConvertToCalendarEvent(message, userConfig.UserId);
                        calendarEvents.Add(calendarEvent);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to convert message {CorrelationId} to calendar event", 
                            message.CorrelationId);
                    }
                }
                
                // Group events by operation type for batch processing
                var createEvents = calendarEvents.Where(e => e.Action == CalendarAction.Create).ToList();
                var updateEvents = calendarEvents.Where(e => e.Action == CalendarAction.Update).ToList();
                var deleteEvents = calendarEvents.Where(e => e.Action == CalendarAction.Delete).ToList();
                
                // Process creates
                if (createEvents.Any())
                {
                    await ProcessOperationBatchAsync(userConfig, createEvents, BatchOperationType.Create, cancellationToken);
                }
                
                // Process updates
                if (updateEvents.Any())
                {
                    await ProcessOperationBatchAsync(userConfig, updateEvents, BatchOperationType.Update, cancellationToken);
                }
                
                // Process deletes
                if (deleteEvents.Any())
                {
                    await ProcessOperationBatchAsync(userConfig, deleteEvents, BatchOperationType.Delete, cancellationToken);
                }
                
                // Log completion
                stopwatch.Stop();
                _logger.LogInformation(
                    "Completed user batch processing for user {UserId}: {CreateCount} creates, {UpdateCount} updates, {DeleteCount} deletes in {ElapsedMs}ms", 
                    userId, createEvents.Count, updateEvents.Count, deleteEvents.Count, stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing user batch for user key {UserKey}", userKey);
            }
            finally
            {
                _batchSemaphore.Release();
            }
        }
        
        /// <summary>
        /// Processes a batch of calendar events for a specific operation type
        /// </summary>
        private async Task ProcessOperationBatchAsync(
            UserSyncConfig userConfig,
            List<CalendarEvent> events,
            BatchOperationType operationType,
            CancellationToken cancellationToken)
        {
            if (events == null || !events.Any())
                return;
            
            try
            {
                _logger.LogInformation("Processing {OperationType} batch of {Count} events for user {UserId}",
                    operationType, events.Count, userConfig.UserId);
                
                // Create batch operations
                var batchOperations = new List<BatchOperation>();
                foreach (var calEvent in events)
                {
                    try
                    {
                        var operation = CreateBatchOperation(calEvent, userConfig.UserId, operationType);
                        batchOperations.Add(operation);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to create batch operation for event {CorrelationId}",
                            calEvent.CorrelationId);
                    }
                }
                
                // Process in chunks of optimal size
                foreach (var chunk in batchOperations.Chunk(OptimalBatchSize))
                {
                    var batchResult = await _graphApiService.ProcessBatchAsync(
                        userConfig, chunk.ToList(), cancellationToken);
                    
                    // Process results
                    foreach (var result in batchResult.Results)
                    {
                        // Find the corresponding calendar event
                        var calEvent = events.FirstOrDefault(e => e.CorrelationId == result.ItemId);
                        if (calEvent != null)
                        {
                            if (result.IsSuccessful)
                            {
                                calEvent.SyncStatus = SyncStatus.Synced;
                                calEvent.LastSyncedAt = DateTimeOffset.UtcNow;
                                calEvent.SyncErrorDetails = null;
                                
                                // Extract event ID for created events
                                if (operationType == BatchOperationType.Create && !string.IsNullOrEmpty(result.Response))
                                {
                                    try
                                    {
                                        var response = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(result.Response);
                                        if (response.TryGetValue("id", out var eventId))
                                        {
                                            calEvent.EventId = eventId.ToString();
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, "Failed to parse event ID from response");
                                    }
                                }
                            }
                            else
                            {
                                calEvent.SyncStatus = SyncStatus.Failed;
                                calEvent.SyncErrorDetails = $"API error: Status {result.StatusCode} - {result.Response}";
                                calEvent.SyncAttempts++;
                            }
                            
                            // Update calendar event in database
                            await _databaseGateway.UpdateCalendarEventAsync(calEvent, cancellationToken);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing {OperationType} batch for user {UserId}",
                    operationType, userConfig.UserId);
                
                // Mark all events as failed
                foreach (var calEvent in events)
                {
                    calEvent.SyncStatus = SyncStatus.Failed;
                    calEvent.SyncErrorDetails = $"Batch processing error: {ex.Message}";
                    calEvent.SyncAttempts++;
                    
                    await _databaseGateway.UpdateCalendarEventAsync(calEvent, cancellationToken);
                }
            }
        }
        
        /// <summary>
        /// Creates a batch operation from a calendar event
        /// </summary>
        private BatchOperation CreateBatchOperation(CalendarEvent calEvent, string userId, BatchOperationType operationType)
        {
            switch (operationType)
            {
                case BatchOperationType.Create:
                    return new BatchOperation
                    {
                        Method = HttpMethod.Post,
                        Url = $"/users/{userId}/calendar/events",
                        Body = calEvent.ToGraphEventPayload(),
                        OperationType = BatchOperationType.Create,
                        ItemId = calEvent.CorrelationId
                    };
                
                case BatchOperationType.Update:
                    return new BatchOperation
                    {
                        Method = HttpMethod.Patch,
                        Url = $"/users/{userId}/calendar/events/{calEvent.EventId}",
                        Body = calEvent.ToGraphEventPayload(),
                        OperationType = BatchOperationType.Update,
                        ItemId = calEvent.CorrelationId
                    };
                
                case BatchOperationType.Delete:
                    return new BatchOperation
                    {
                        Method = HttpMethod.Delete,
                        Url = $"/users/{userId}/calendar/events/{calEvent.EventId}",
                        OperationType = BatchOperationType.Delete,
                        ItemId = calEvent.CorrelationId
                    };
                
                default:
                    throw new ArgumentException($"Unsupported operation type: {operationType}");
            }
        }
        
        /// <summary>
        /// Converts an appointment message to a calendar event
        /// </summary>
        private CalendarEvent ConvertToCalendarEvent(AppointmentMessage message, string userId)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));
            
            // Create calendar event from message
            var calendarEvent = new CalendarEvent
            {
                EventId = message.CalendarEventId, // May be null for new events
                CorrelationId = message.CorrelationId,
                UserId = userId,
                AppointmentId = message.AppointmentId,
                Subject = message.Subject,
                Body = message.Body,
                BodyType = message.BodyType,
                StartTime = message.StartTime,
                EndTime = message.EndTime,
                Location = message.Location,
                Attendees = message.Attendees?.Select(a => new Attendee
                {
                    Email = a.Email,
                    Name = a.Name,
                    Type = a.Type
                }).ToList(),
                IsAllDay = message.IsAllDay,
                Importance = message.Importance,
                Sensitivity = message.Sensitivity,
                Action = message.IsDelete ? CalendarAction.Delete : 
                    (string.IsNullOrEmpty(message.CalendarEventId) ? CalendarAction.Create : CalendarAction.Update),
                LastSyncAttemptedAt = DateTimeOffset.UtcNow,
                SyncAttempts = 1
            };
            
            return calendarEvent;
        }
    }

    /// <summary>
    /// Results of batch processing multiple appointment messages
    /// </summary>
    public class BatchProcessingResult
    {
        /// <summary>
        /// Total number of messages processed in the batch
        /// </summary>
        public int TotalMessages { get; set; }
        
        /// <summary>
        /// Number of messages successfully processed
        /// </summary>
        public int SuccessfulMessages { get; set; }
        
        /// <summary>
        /// Number of messages that failed processing
        /// </summary>
        public int FailedMessages { get; set; }
        
        /// <summary>
        /// Number of messages that were skipped (e.g., no user config found)
        /// </summary>
        public int SkippedMessages { get; set; }
        
        /// <summary>
        /// List of user IDs that were successfully processed
        /// </summary>
        public List<string> SuccessfulUsers { get; set; } = new List<string>();
        
        /// <summary>
        /// List of user IDs with some failed operations
        /// </summary>
        public List<string> PartiallyFailedUsers { get; set; } = new List<string>();
        
        /// <summary>
        /// List of user IDs with completely failed processing
        /// </summary>
        public List<string> FailedUsers { get; set; } = new List<string>();
        
        /// <summary>
        /// When batch processing started
        /// </summary>
        public DateTimeOffset StartedAt { get; set; }
        
        /// <summary>
        /// When batch processing completed
        /// </summary>
        public DateTimeOffset CompletedAt { get; set; }
        
        /// <summary>
        /// Total processing time
        /// </summary>
        public TimeSpan ProcessingTime { get; set; }
    }

    /// <summary>
    /// Results of processing messages for a specific user
    /// </summary>
    public class UserBatchResult
    {
        /// <summary>
        /// User ID
        /// </summary>
        public string UserId { get; set; }
        
        /// <summary>
        /// Total operations attempted for this user
        /// </summary>
        public int TotalOperations { get; set; }
        
        /// <summary>
        /// Number of successful operations
        /// </summary>
        public int SuccessfulOperations { get; set; }
        
        /// <summary>
        /// Number of failed operations
        /// </summary>
        public int FailedOperations { get; set; }
    }
}
