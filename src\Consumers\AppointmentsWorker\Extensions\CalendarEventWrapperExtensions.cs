using System;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Extensions
{
    /// <summary>
    /// Extension methods for CalendarEventWrapper to support functional programming patterns
    /// </summary>
    public static class CalendarEventWrapperExtensions
    {
        /// <summary>
        /// Creates a copy of the CalendarEventWrapper with success status and updated properties
        /// </summary>
        /// <param name="wrapper">The original wrapper</param>
        /// <param name="eventId">The event ID from Graph API</param>
        /// <param name="syncTime">The sync timestamp</param>
        /// <returns>Updated wrapper with success status</returns>
        public static CalendarEventWrapper WithSuccess(this CalendarEventWrapper wrapper, string eventId, DateTimeOffset syncTime)
        {
            if (wrapper == null) throw new ArgumentNullException(nameof(wrapper));

            return new CalendarEventWrapper
            {
                // Copy all original properties
                CorrelationId = wrapper.CorrelationId,
                AppointmentId = wrapper.AppointmentId,
                FirmId = wrapper.FirmId,
                UserId = wrapper.UserId,
                GraphEvent = wrapper.GraphEvent,
                CreatedAt = wrapper.CreatedAt,
                ModifiedAt = syncTime,
                
                // Update with success status
                EventId = eventId,
                LastSyncedAt = syncTime,
                SyncStatus = SyncStatus.Synced,
                SyncErrorDetails = null
            };
        }

        /// <summary>
        /// Creates a copy of the CalendarEventWrapper with failure status and error details
        /// </summary>
        /// <param name="wrapper">The original wrapper</param>
        /// <param name="errorDetails">The error details</param>
        /// <returns>Updated wrapper with failure status</returns>
        public static CalendarEventWrapper WithFailure(this CalendarEventWrapper wrapper, string errorDetails)
        {
            if (wrapper == null) throw new ArgumentNullException(nameof(wrapper));

            return new CalendarEventWrapper
            {
                // Copy all original properties
                CorrelationId = wrapper.CorrelationId,
                AppointmentId = wrapper.AppointmentId,
                FirmId = wrapper.FirmId,
                UserId = wrapper.UserId,
                EventId = wrapper.EventId,
                GraphEvent = wrapper.GraphEvent,
                CreatedAt = wrapper.CreatedAt,
                ModifiedAt = DateTimeOffset.UtcNow,
                LastSyncedAt = wrapper.LastSyncedAt,
                
                // Update with failure status
                SyncStatus = SyncStatus.Failed,
                SyncErrorDetails = errorDetails
            };
        }

        /// <summary>
        /// Creates a copy of the CalendarEventWrapper with updated status for update operations
        /// </summary>
        /// <param name="wrapper">The original wrapper</param>
        /// <param name="syncTime">The sync timestamp</param>
        /// <returns>Updated wrapper with success status</returns>
        public static CalendarEventWrapper WithUpdateSuccess(this CalendarEventWrapper wrapper, DateTimeOffset syncTime)
        {
            if (wrapper == null) throw new ArgumentNullException(nameof(wrapper));

            return new CalendarEventWrapper
            {
                // Copy all original properties
                CorrelationId = wrapper.CorrelationId,
                AppointmentId = wrapper.AppointmentId,
                FirmId = wrapper.FirmId,
                UserId = wrapper.UserId,
                EventId = wrapper.EventId,
                GraphEvent = wrapper.GraphEvent,
                CreatedAt = wrapper.CreatedAt,
                
                // Update with success status
                ModifiedAt = syncTime,
                LastSyncedAt = syncTime,
                SyncStatus = SyncStatus.Synced,
                SyncErrorDetails = null
            };
        }
    }
}
