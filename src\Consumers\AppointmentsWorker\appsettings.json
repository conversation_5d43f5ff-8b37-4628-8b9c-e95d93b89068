{"Application": {"ClientId": "151b6300-7b83-4530-9838-7ca8f25d2efd", "ClientSecret": "**********************************", "TenantId": "common", "ApplicationName": "AppointmentsConsumer", "EnableDetailedLogs": true, "RetryMaxAttempts": 5, "RetryDelayInSeconds": 2, "CircuitBreakerThreshold": 5, "CircuitBreakerDurationInSeconds": 60, "TimeoutInSeconds": 30, "DeadLetterQueueEnabled": true, "DeadLetterQueueTopic": "appointments-dead-letter"}, "Kafka": {"BootstrapServers": "kafka-f68bd74-caretlegal-data-bus.b.aivencloud.com:15904", "ConsumerGroupId": "appointments-consumer-group", "ConsumerTopic": "appointments-topic", "SchemaRegistryUrl": "http://localhost:8081", "UseSecureConnection": true, "SaslUsername": "a<PERSON><PERSON><PERSON><PERSON>", "SaslPassword": "AVNS_Boi1zX2ngjmFsqmsd1s", "SchemaRegistryUsername": "", "SchemaRegistryPassword": "", "MessageTimeoutMs": 300000, "MessageMaxBytes": 10485760, "BatchSize": 100, "LingerMs": 5, "Acks": "all", "EnableIdempotence": true}, "Database": {"SystemConnectionString": "Data Source=zoladevdb.amicusdev.amicuscreative.com;Initial Catalog=zola-bronze3;Trusted_Connection=True;max pool size=500;TrustServerCertificate=True", "MaxRetries": 5, "RetryDelaySeconds": 2, "CommandTimeoutSeconds": 30, "EnableSensitiveDataLogging": false, "EnableDetailedErrors": true, "ConnectionPooling": {"MinPoolSize": 5, "MaxPoolSize": 500, "ConnectionLifetimeSeconds": 300}, "TenantDatabaseSettings": {"ConnectionStringTemplate": "Server={0};Database={1};User Id={2};Password=***;TrustServerCertificate=True", "ConnectionTimeoutSeconds": 30}}, "MessageChannel": {"Capacity": 1000, "SingleReaderMode": true}, "Datadog": {"Enabled": false, "ApiKey": "your-datadog-api-key", "Url": "https://http-intake.logs.datadoghq.com", "AgentHost": "localhost", "AgentPort": 8126, "Environment": "Development", "Service": "AppointmentsConsumer", "Version": "1.0.0", "Tags": ["env:development", "project:caretlegal", "component:appointments"]}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}, {"Name": "File", "Args": {"path": "logs/appointments-consumer-.log", "rollingInterval": "Day", "formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact", "retainedFileCountLimit": 7}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "MicrosoftGraphApi": {"Login": "https://login.microsoftonline.com", "GraphUrlBase": "https://graph.microsoft.com/v1.0", "ClientId": "151b6300-7b83-4530-9838-7ca8f25d2efd", "ClientSecret": "**********************************", "GraphScopes": ["https://graph.microsoft.com/.default"], "GraphRedirectUrl": "https://dev.zolastaging.com/Settings/UserSettings/OutlookFolderMapping.aspx", "RedirectUrl": "https://dev.zolastaging.com/calendar/sync/outlookredirecturlcalendarsync.aspx", "TenantId": "common", "BatchSize": 20, "ThrottlingLimitRequests": 2000, "ThrottlingLimitPeriodMinutes": 1}, "GraphApi": {"BaseUrl": "https://graph.microsoft.com/v1.0", "Scopes": ["https://graph.microsoft.com/.default"], "BatchSize": 20, "ThrottlingLimitRequests": 2000, "ThrottlingLimitPeriodMinutes": 1}, "Resilience": {"GraphApi": {"RetryCount": 3, "RetryDelayInSeconds": 2, "CircuitBreakerThreshold": 5, "CircuitBreakerDurationInSeconds": 60}, "Database": {"RetryCount": 5, "RetryDelayInSeconds": 1, "CircuitBreakerThreshold": 10, "CircuitBreakerDurationInSeconds": 120}, "TokenAcquisition": {"RetryCount": 3, "RetryDelayInSeconds": 1, "CircuitBreakerThreshold": 5, "CircuitBreakerDurationInSeconds": 300}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*"}