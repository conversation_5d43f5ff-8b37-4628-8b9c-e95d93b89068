using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Confluent.Kafka;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Service for replaying failed messages from dead letter queue
    /// </summary>
    public class MessageReplayService
    {
        private readonly ILogger<MessageReplayService> _logger;
        private readonly KafkaOptions _kafkaOptions;
        private readonly ApplicationOptions _appOptions;
        private readonly ResiliencePolicies _resilience;
        private readonly ConcurrentDictionary<string, DateTime> _replayedMessages = new();
        
        /// <summary>
        /// Creates a new instance of MessageReplayService
        /// </summary>
        public MessageReplayService(
            ILogger<MessageReplayService> logger,
            IOptions<KafkaOptions> kafkaOptions,
            IOptions<ApplicationOptions> appOptions,
            ResiliencePolicies resilience)
        {
            _logger = logger;
            _kafkaOptions = kafkaOptions.Value;
            _appOptions = appOptions.Value;
            _resilience = resilience;
        }
        
        /// <summary>
        /// Replays a failed message back to the main topic
        /// </summary>
        /// <param name="message">The message to replay</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if replay was successful, false otherwise</returns>
        public async Task<bool> ReplayMessageAsync(AppointmentMessage message, CancellationToken cancellationToken)
        {
            if (message == null)
            {
                _logger.LogWarning("Cannot replay null message");
                return false;
            }

            var messageId = message.MessageId;
            
            // Prevent duplicate replays within a short timeframe
            if (_replayedMessages.TryGetValue(messageId, out var lastReplayTime))
            {
                if (DateTime.UtcNow.Subtract(lastReplayTime).TotalMinutes < 5)
                {
                    _logger.LogWarning("Message {MessageId} was already replayed recently, skipping duplicate replay", messageId);
                    return false;
                }
            }
            
            try
            {
                var result = await _resilience.ConsumerPolicy.ExecuteAsync(async (ctx) =>
                {
                    // Configure producer
                    var config = new ProducerConfig
                    {
                        BootstrapServers = _kafkaOptions.BootstrapServers,
                        ClientId = $"{_appOptions.ServiceName}-replayer",
                    };
                    
                    // Add SASL configuration if available
                    if (!string.IsNullOrEmpty(_kafkaOptions.SaslUsername) && !string.IsNullOrEmpty(_kafkaOptions.SaslPassword))
                    {
                        config.SaslUsername = _kafkaOptions.SaslUsername;
                        config.SaslPassword = _kafkaOptions.SaslPassword;
                        config.SaslMechanism = SaslMechanism.Plain;
                        config.SecurityProtocol = SecurityProtocol.SaslSsl;
                    }
                    
                    using var producer = new ProducerBuilder<string, string>(config).Build();
                    
                    // Prepare message
                    message.ModifiedDate = DateTimeOffset.UtcNow;
                    message.ModifiedBy = $"{_appOptions.ServiceName}-replayer";
                      // Use the original tenant and appointment IDs as key
                    var key = $"{message.TenantId}:{message.AppointmentId}";
                    
                    // Serialize message to JSON using our standard JsonHelper
                    var serializedMessage = Serialization.JsonHelper.Serialize(message);
                    
                    // Build message with headers to mark as a replay
                    var kafkaMessage = new Message<string, string>
                    {
                        Key = key,
                        Value = serializedMessage,
                        Headers = new Headers
                        {
                            { "X-Replay", System.Text.Encoding.UTF8.GetBytes("true") },
                            { "X-Original-Timestamp", System.Text.Encoding.UTF8.GetBytes(DateTime.UtcNow.ToString("o")) },
                            { "X-Message-Id", System.Text.Encoding.UTF8.GetBytes(messageId) }
                        }
                    };
                    
                    // Send message
                    var deliveryReport = await producer.ProduceAsync(_kafkaOptions.TopicName, kafkaMessage, cancellationToken);
                    
                    // Update replay tracking
                    _replayedMessages.AddOrUpdate(messageId, DateTime.UtcNow, (_, _) => DateTime.UtcNow);
                    
                    return deliveryReport.Status == PersistenceStatus.Persisted;
                    
                }, new Context("ReplayMessage"));
                
                if (result)
                {
                    _logger.LogInformation("Successfully replayed message {MessageId} to topic {Topic}",
                        messageId, _kafkaOptions.TopicName);
                }
                else
                {
                    _logger.LogWarning("Failed to persist replayed message {MessageId} to topic {Topic}",
                        messageId, _kafkaOptions.TopicName);
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error replaying message {MessageId}", messageId);
                return false;
            }
        }
        
        /// <summary>
        /// Replays messages from the dead letter queue that match the specified criteria
        /// </summary>
        /// <param name="filter">Optional filter predicate</param>
        /// <param name="maxMessages">Maximum number of messages to replay</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Number of successfully replayed messages</returns>
        public async Task<int> ReplayFromDeadLetterQueueAsync(
            Func<AppointmentMessage, bool>? filter = null,
            int maxMessages = 100,
            CancellationToken cancellationToken = default)
        {
            if (!_appOptions.EnableDeadLetterQueue)
            {
                _logger.LogWarning("Dead letter queue is disabled, cannot replay messages");
                return 0;
            }
            
            var successCount = 0;
            
            try
            {
                // Configure consumer for dead letter queue
                var consumerConfig = new ConsumerConfig
                {
                    BootstrapServers = _kafkaOptions.BootstrapServers,
                    GroupId = $"{_kafkaOptions.ConsumerGroupId}-replayer",
                    AutoOffsetReset = Enum.Parse<AutoOffsetReset>(_kafkaOptions.AutoOffsetReset, true),
                    EnableAutoCommit = true,
                    MaxPollIntervalMs = _kafkaOptions.MaxPollIntervalMs
                };
                
                // Add SASL configuration if available
                if (!string.IsNullOrEmpty(_kafkaOptions.SaslUsername) && !string.IsNullOrEmpty(_kafkaOptions.SaslPassword))
                {
                    consumerConfig.SaslUsername = _kafkaOptions.SaslUsername;
                    consumerConfig.SaslPassword = _kafkaOptions.SaslPassword;
                    consumerConfig.SaslMechanism = SaslMechanism.Plain;
                    consumerConfig.SecurityProtocol = SecurityProtocol.SaslSsl;
                }
                
                using var consumer = new ConsumerBuilder<string, string>(consumerConfig).Build();
                consumer.Subscribe(_appOptions.DeadLetterTopic);
                
                var messagesProcessed = 0;
                var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                cts.CancelAfter(TimeSpan.FromMinutes(5)); // Safety timeout
                
                while (!cts.IsCancellationRequested && messagesProcessed < maxMessages)
                {
                    try
                    {
                        var consumeResult = consumer.Consume(cts.Token);
                        
                        if (consumeResult == null || consumeResult.IsPartitionEOF)
                        {
                            break;
                        }
                        
                        messagesProcessed++;
                        
                        // Deserialize the message
                        var message = System.Text.Json.JsonSerializer.Deserialize<AppointmentMessage>(consumeResult.Message.Value);
                        
                        if (message != null && (filter == null || filter(message)))
                        {
                            // Replay the message
                            var replaySuccess = await ReplayMessageAsync(message, cts.Token);
                            
                            if (replaySuccess)
                            {
                                successCount++;
                            }
                        }
                    }
                    catch (ConsumeException ex)
                    {
                        _logger.LogError(ex, "Error consuming from dead letter queue");
                    }
                }
                
                consumer.Close();
                
                _logger.LogInformation("Replayed {SuccessCount} of {ProcessedCount} messages from dead letter queue",
                    successCount, messagesProcessed);
                
                return successCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error replaying messages from dead letter queue");
                return successCount;
            }
        }
    }
}
