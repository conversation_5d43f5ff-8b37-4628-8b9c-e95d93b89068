# Microsoft Graph API Batching Strategy

## Overview

This document explains the intelligent batching strategy implemented in `GraphApiService` for optimal performance with Microsoft Graph API calendar operations.

## Batch vs Individual Request Analysis

### Performance Characteristics

| Scenario | Approach | Network Calls | Latency | Throughput | Best For |
|----------|----------|---------------|---------|------------|----------|
| 1 Event | Individual | 1 | ~100ms | High | Single operations |
| 2-3 Events | Parallel Individual | 2-3 | ~100ms | Very High | Small batches |
| 4-20 Events | Throttled Parallel | 4-20 | ~100ms each | High | Medium batches |
| 20+ Events | Chunked Processing | Multiple chunks | ~100ms per chunk | Optimal | Large batches |

### Why Not Always Use Graph Batch API?

1. **Complexity**: Graph SDK v5 batch API requires complex setup
2. **Overhead**: Batch wrapper adds ~50-100ms for small operations
3. **Error Handling**: Individual requests provide clearer error isolation
4. **Rate Limiting**: Parallel requests with throttling is more predictable

## Implementation Strategy

### Smart Batching Logic

```csharp
// Always use batch methods, but optimize internally
public async Task<CalendarEventWrapper> CreateEventAsync(...)
{
    // Delegates to batch method for consistency
    var results = await CreateEventsAsync(userConfig, new[] { calendarEvent }, cancellationToken);
    return results.First();
}

public async Task<IEnumerable<CalendarEventWrapper>> CreateEventsAsync(...)
{
    // Smart internal batching based on count
    if (events.Count <= 3)
        return await ProcessSmallBatch(events);
    else
        return await ProcessLargeBatch(events);
}
```

### Small Batch Processing (1-3 items)

- **Approach**: Fully parallel individual requests
- **Concurrency**: Unlimited (3 max anyway)
- **Benefits**: Lowest latency, highest throughput
- **Use Case**: Real-time single event operations

```csharp
var tasks = events.Select(async event => 
{
    return await graphServiceClient.Users[userId]
        .Calendar.Events
        .PostAsync(event.GraphEvent, cancellationToken);
});
return await Task.WhenAll(tasks);
```

### Large Batch Processing (4+ items)

- **Approach**: Throttled parallel requests
- **Concurrency**: Limited to 5 concurrent requests
- **Benefits**: Prevents API throttling, maintains throughput
- **Use Case**: Bulk operations, initial sync

```csharp
var semaphore = new SemaphoreSlim(5, 5);
var tasks = events.Select(async event => 
{
    await semaphore.WaitAsync(cancellationToken);
    try
    {
        return await ProcessSingleEvent(event);
    }
    finally
    {
        semaphore.Release();
    }
});
```

## API Design

### Consistent Interface

All methods provide both single and batch variants:

```csharp
// Single operations (internally use batch)
Task<CalendarEventWrapper> CreateEventAsync(...)
Task<CalendarEventWrapper> UpdateEventAsync(...)
Task<bool> DeleteEventAsync(...)

// Batch operations (optimized implementation)
Task<IEnumerable<CalendarEventWrapper>> CreateEventsAsync(...)
Task<IEnumerable<CalendarEventWrapper>> UpdateEventsAsync(...)
Task<Dictionary<string, bool>> DeleteEventsAsync(...)
```

### Usage Patterns

```csharp
// Single event - use either method
var result = await graphApiService.CreateEventAsync(userConfig, event, cancellationToken);
// OR
var results = await graphApiService.CreateEventsAsync(userConfig, new[] { event }, cancellationToken);

// Multiple events - use batch method
var results = await graphApiService.CreateEventsAsync(userConfig, events, cancellationToken);
```

## Performance Benefits

### Network Efficiency

- **Single Event**: 1 request, ~100ms total
- **3 Events Parallel**: 3 requests, ~100ms total (vs ~300ms sequential)
- **10 Events Throttled**: 10 requests, ~200ms total (vs ~1000ms sequential)
- **50 Events Chunked**: 3 chunks of ~17 events each, ~300ms total

### Rate Limit Optimization

- **Throttling**: SemaphoreSlim prevents overwhelming Graph API
- **Retry Policies**: Built-in resilience policies handle transient failures
- **Error Isolation**: Individual request failures don't affect others

### Memory Efficiency

- **Streaming**: No large batch payloads in memory
- **Parallel Processing**: Optimal CPU utilization
- **Garbage Collection**: Minimal object allocation

## Error Handling

### Individual Request Benefits

```csharp
// Each event gets individual error handling
foreach (var task in tasks)
{
    try
    {
        var result = await task;
        event.SyncStatus = SyncStatus.Synced;
    }
    catch (ServiceException ex)
    {
        event.SyncStatus = SyncStatus.Failed;
        event.SyncErrorDetails = $"Graph API error: {ex.ResponseStatusCode}";
    }
}
```

### Partial Success Handling

- **Granular Status**: Each event has individual sync status
- **Retry Logic**: Failed events can be retried independently
- **Audit Trail**: Detailed error information per event

## Monitoring and Telemetry

### Metrics Tracked

- **Request Count**: Individual vs batch operations
- **Latency**: Per-operation and total batch time
- **Success Rate**: Per-event success/failure rates
- **Throttling**: Semaphore wait times

### Logging Strategy

```csharp
// Batch-level logging
_logger.LogInformation("Creating {Count} events for user {UserId}", events.Count, userId);

// Individual event logging
_logger.LogDebug("Successfully created event {EventId}", eventId);
_logger.LogError("Failed to create event: {ErrorCode}", ex.ResponseStatusCode);
```

## Configuration

### Tunable Parameters

```csharp
// Small batch threshold (currently 3)
private const int SmallBatchThreshold = 3;

// Concurrent request limit (currently 5)
private const int MaxConcurrentRequests = 5;

// Chunk size for large batches (currently 20)
private const int ChunkSize = 20;
```

### Environment-Specific Tuning

- **Development**: Higher concurrency for faster testing
- **Production**: Conservative limits to prevent throttling
- **High-Volume**: Larger chunks with more aggressive parallelism

## Conclusion

This intelligent batching strategy provides:

1. **Optimal Performance**: Right approach for each scenario
2. **Consistent API**: Same interface regardless of batch size
3. **Robust Error Handling**: Individual request isolation
4. **Scalable Architecture**: Handles 1 to 1000+ events efficiently
5. **Production Ready**: Built-in throttling and resilience

The implementation automatically chooses the best strategy based on the number of items, providing excellent performance for both single operations and bulk processing scenarios.
