using System;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Contexts
{
    /// <summary>
    /// Provides access to the system database
    /// </summary>
    public class SystemDbContext
    {
        private readonly ILogger<SystemDbContext> _logger;
        private readonly string _connectionString;
        private readonly ResiliencePolicies _resilience;
        
        /// <summary>
        /// Creates a new instance of SystemDbContext
        /// </summary>
        public SystemDbContext(
            ILogger<SystemDbContext> logger,
            IOptions<DatabaseOptions> options,
            ResiliencePolicies resilience)
        {
            _logger = logger;
            _connectionString = options.Value.SystemDbConnectionString;
            _resilience = resilience;
            
            if (string.IsNullOrEmpty(_connectionString))
            {
                throw new ArgumentException("System database connection string is not configured");
            }
        }
        
        /// <summary>
        /// Creates a new connection to the system database
        /// </summary>
        /// <returns>An open database connection</returns>
        public async Task<SqlConnection> CreateConnectionAsync()
        {
            try
            {
                var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();
                return connection;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create connection to system database");
                throw new DatabaseException("Failed to create connection to system database", isTransient: true, innerException: ex);
            }
        }
        
        /// <summary>
        /// Executes a query with parameters and returns a single value
        /// </summary>
        /// <typeparam name="T">Return value type</typeparam>
        /// <param name="sql">SQL query text</param>
        /// <param name="parameters">Query parameters</param>
        /// <returns>Query result</returns>
        public async Task<T> ExecuteScalarAsync<T>(string sql, params SqlParameter[] parameters)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                using var connection = await CreateConnectionAsync();
                using var command = connection.CreateCommand();
                command.CommandText = sql;
                command.CommandType = CommandType.Text;
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                var result = await command.ExecuteScalarAsync();
                return result is DBNull ? default : (T)Convert.ChangeType(result, typeof(T));
            },
            new Context("ExecuteScalar"));
        }
        
        /// <summary>
        /// Executes a non-query command with parameters
        /// </summary>
        /// <param name="sql">SQL command text</param>
        /// <param name="parameters">Command parameters</param>
        /// <returns>Number of affected rows</returns>
        public async Task<int> ExecuteNonQueryAsync(string sql, params SqlParameter[] parameters)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                using var connection = await CreateConnectionAsync();
                using var command = connection.CreateCommand();
                command.CommandText = sql;
                command.CommandType = CommandType.Text;
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                return await command.ExecuteNonQueryAsync();
            },
            new Context("ExecuteNonQuery"));
        }
        
        /// <summary>
        /// Executes a query and processes the results with a callback
        /// </summary>
        /// <typeparam name="T">Return value type</typeparam>
        /// <param name="sql">SQL query text</param>
        /// <param name="readerCallback">Callback to process the data reader</param>
        /// <param name="parameters">Query parameters</param>
        /// <returns>Query result processed by the callback</returns>
        public async Task<T> ExecuteReaderAsync<T>(string sql, Func<SqlDataReader, Task<T>> readerCallback, params SqlParameter[] parameters)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                using var connection = await CreateConnectionAsync();
                using var command = connection.CreateCommand();
                command.CommandText = sql;
                command.CommandType = CommandType.Text;
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                using var reader = await command.ExecuteReaderAsync();
                return await readerCallback(reader);
            },
            new Context("ExecuteReader"));
        }
        
        /// <summary>
        /// Executes a stored procedure and processes the results with a callback
        /// </summary>
        /// <typeparam name="T">Return value type</typeparam>
        /// <param name="procedureName">Stored procedure name</param>
        /// <param name="readerCallback">Callback to process the data reader</param>
        /// <param name="parameters">Procedure parameters</param>
        /// <returns>Procedure result processed by the callback</returns>
        public async Task<T> ExecuteStoredProcedureAsync<T>(string procedureName, Func<SqlDataReader, Task<T>> readerCallback, params SqlParameter[] parameters)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                using var connection = await CreateConnectionAsync();
                using var command = connection.CreateCommand();
                command.CommandText = procedureName;
                command.CommandType = CommandType.StoredProcedure;
                
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                
                using var reader = await command.ExecuteReaderAsync();
                return await readerCallback(reader);
            },
            new Context("ExecuteStoredProcedure"));
        }
        
        /// <summary>
        /// Executes a transaction with multiple commands
        /// </summary>
        /// <typeparam name="T">Return value type</typeparam>
        /// <param name="transactionAction">Action to execute within the transaction</param>
        /// <returns>Result of the transaction action</returns>
        public async Task<T> ExecuteTransactionAsync<T>(Func<SqlConnection, SqlTransaction, Task<T>> transactionAction)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                using var connection = await CreateConnectionAsync();
                using var transaction = connection.BeginTransaction();
                
                try
                {
                    var result = await transactionAction(connection, transaction);
                    transaction.Commit();
                    return result;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            },
            new Context("ExecuteTransaction"));
        }
    }
}
