using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Contexts;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Gateways
{
    /// <summary>
    /// Gateway for database operations
    /// </summary>
    public class DatabaseGateway
    {
        private readonly ILogger<DatabaseGateway> _logger;
        private readonly TenantConnectionFactory _connectionFactory;
        private readonly SystemDbContext _systemDbContext;
        private readonly ResiliencePolicies _resilience;
        private readonly ApplicationOptions _options;

        /// <summary>
        /// Creates a new instance of DatabaseGateway
        /// </summary>
        public DatabaseGateway(
            ILogger<DatabaseGateway> logger,
            TenantConnectionFactory connectionFactory,
            SystemDbContext systemDbContext,
            ResiliencePolicies resilience,
            IOptions<ApplicationOptions> options)
        {
            _logger = logger;
            _connectionFactory = connectionFactory;
            _systemDbContext = systemDbContext;
            _resilience = resilience;
            _options = options.Value;
        }

        /// <summary>
        /// Gets user sync configuration for the specified user
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>User sync configuration or null if not found</returns>
        public async Task<UserSyncConfig?> GetUserSyncConfigAsync(string tenantId, string userId, CancellationToken cancellationToken = default)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    using var connection = await _connectionFactory.CreateConnectionAsync(tenantId);
                    using var command = _connectionFactory.CreateCommand(connection);

                    command.CommandText = @"
                        SELECT Id, UserId, TenantId, Email, Security, RefreshToken,
                               TokenExpiry, IsValid, SyncEnabled, LastSyncTime, MaxRetries,
                               RetryCount, LastRefreshAttempt, CreatedAt, UpdatedAt, SyncSettings
                        FROM T_UserSyncConfig
                        WHERE TenantId = @TenantId AND UserId = @UserId";

                    command.Parameters.AddWithValue("@TenantId", tenantId);
                    command.Parameters.AddWithValue("@UserId", userId);

                    using var reader = await command.ExecuteReaderAsync(cancellationToken);

                    if (await reader.ReadAsync(cancellationToken))
                    {
                        return new UserSyncConfig
                        {
                            Id = reader.GetInt32(0),
                            UserId = reader.GetInt32(1),
                            FirmId = reader.GetInt32(2),
                            Provider = reader.IsDBNull(3) ? null : reader.GetString(3),
                            Security = reader.IsDBNull(4) ? null : reader.GetString(4),
                            AccountEmail = reader.IsDBNull(5) ? null : reader.GetString(5),
                            IsImmutableIdsInserted = reader.GetBoolean(6),
                            IsInitialSyncEmailSent = reader.GetBoolean(7),
                            ActivityStatusId = reader.GetInt32(8)
                        };
                    }

                    return null;
                }
                catch (Exception ex) when (!(ex is DatabaseException))
                {
                    _logger.LogError(ex, "Error retrieving user sync config for tenant {TenantId}, user {UserId}", tenantId, userId);
                    throw new DatabaseException($"Error retrieving user sync config: {ex.Message}", tenantId, userId, true, ex);
                }
            },
            new Context("GetUserSyncConfig"));
        }

        /// <summary>
        /// Updates a user's token information
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="security">Security token JSON</param>
        /// <param name="refreshToken">Refresh token</param>
        /// <param name="tokenExpiry">Token expiry date</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if successful</returns>
        public async Task<bool> UpdateUserTokenAsync(string tenantId, string userId, string security, string refreshToken, DateTime tokenExpiry, CancellationToken cancellationToken = default)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    using var connection = await _connectionFactory.CreateConnectionAsync(tenantId);
                    using var command = _connectionFactory.CreateCommand(connection);

                    command.CommandText = @"
                        UPDATE T_UserSyncConfig
                        SET Security = @Security,
                            RefreshToken = @RefreshToken,
                            TokenExpiry = @TokenExpiry,
                            RetryCount = 0,
                            LastRefreshAttempt = @LastRefreshAttempt,
                            UpdatedAt = @UpdatedAt
                        WHERE TenantId = @TenantId AND UserId = @UserId";

                    command.Parameters.AddWithValue("@TenantId", tenantId);
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.Parameters.AddWithValue("@Security", security);
                    command.Parameters.AddWithValue("@RefreshToken", refreshToken);
                    command.Parameters.AddWithValue("@TokenExpiry", tokenExpiry);
                    command.Parameters.AddWithValue("@LastRefreshAttempt", DateTime.UtcNow);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.UtcNow);

                    var rowsAffected = await command.ExecuteNonQueryAsync(cancellationToken);
                    return rowsAffected > 0;
                }
                catch (Exception ex) when (!(ex is DatabaseException))
                {
                    _logger.LogError(ex, "Error updating user token for tenant {TenantId}, user {UserId}", tenantId, userId);
                    throw new DatabaseException($"Error updating user token: {ex.Message}", tenantId, userId, true, ex);
                }
            },
            new Context("UpdateUserToken"));
        }

        /// <summary>
        /// Invalidates a user's token
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if successful</returns>
        public async Task<bool> InvalidateUserTokenAsync(string tenantId, string userId, CancellationToken cancellationToken = default)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    using var connection = await _connectionFactory.CreateConnectionAsync(tenantId);
                    using var command = _connectionFactory.CreateCommand(connection);

                    command.CommandText = @"
                        UPDATE T_UserSyncConfig
                        SET IsValid = 0,
                            UpdatedAt = @UpdatedAt
                        WHERE TenantId = @TenantId AND UserId = @UserId";

                    command.Parameters.AddWithValue("@TenantId", tenantId);
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.UtcNow);

                    var rowsAffected = await command.ExecuteNonQueryAsync(cancellationToken);

                    if (rowsAffected > 0)
                    {
                        _logger.LogWarning("Invalidated token for tenant {TenantId}, user {UserId}", tenantId, userId);
                        return true;
                    }

                    return false;
                }
                catch (Exception ex) when (!(ex is DatabaseException))
                {
                    _logger.LogError(ex, "Error invalidating token for tenant {TenantId}, user {UserId}", tenantId, userId);
                    throw new DatabaseException($"Error invalidating token: {ex.Message}", tenantId, userId, true, ex);
                }
            },
            new Context("InvalidateUserToken"));
        }

        /// <summary>
        /// Updates the last sync date for a user
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if successful</returns>
        public async Task<bool> UpdateLastSyncDateAsync(string tenantId, string userId, CancellationToken cancellationToken = default)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    using var connection = await _connectionFactory.CreateConnectionAsync(tenantId);
                    using var command = _connectionFactory.CreateCommand(connection);

                    command.CommandText = @"
                        UPDATE T_UserSyncConfig
                        SET LastSyncTime = @LastSyncTime,
                            UpdatedAt = @UpdatedAt
                        WHERE TenantId = @TenantId AND UserId = @UserId";

                    command.Parameters.AddWithValue("@TenantId", tenantId);
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.Parameters.AddWithValue("@LastSyncTime", DateTime.UtcNow);
                    command.Parameters.AddWithValue("@UpdatedAt", DateTime.UtcNow);

                    var rowsAffected = await command.ExecuteNonQueryAsync(cancellationToken);
                    return rowsAffected > 0;
                }
                catch (Exception ex) when (!(ex is DatabaseException))
                {
                    _logger.LogError(ex, "Error updating last sync date for tenant {TenantId}, user {UserId}", tenantId, userId);
                    throw new DatabaseException($"Error updating last sync date: {ex.Message}", tenantId, userId, true, ex);
                }
            },
            new Context("UpdateLastSyncDate"));
        }

        /// <summary>
        /// Inserts or updates a calendar event
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="calendarEvent">Calendar event data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if successful</returns>
        public async Task<bool> UpsertCalendarEventAsync(string tenantId, CalendarEventWrapper calendarEvent, CancellationToken cancellationToken = default)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    using var connection = await _connectionFactory.CreateConnectionAsync(tenantId);
                    using var command = _connectionFactory.CreateCommand(connection);

                    command.CommandText = @"
                        MERGE INTO CalendarEvents AS target
                        USING (SELECT @EventId, @UserId, @AppointmentId) AS source (EventId, UserId, AppointmentId)
                        ON target.EventId = source.EventId
                        WHEN MATCHED THEN
                            UPDATE SET
                                Title = @Title,
                                Description = @Description,
                                StartTime = @StartTime,
                                EndTime = @EndTime,
                                Location = @Location,
                                IsAllDay = @IsAllDay,
                                Status = @Status,
                                ExternalEventId = @ExternalEventId,
                                ExternalSystem = @ExternalSystem,
                                SyncStatus = @SyncStatus,
                                ModifiedAt = @ModifiedAt
                        WHEN NOT MATCHED THEN
                            INSERT (
                                EventId, TenantId, UserId, Title, Description, StartTime, EndTime,
                                Location, IsAllDay, Status, ExternalEventId, ExternalSystem,
                                Operation, AppointmentId, IsPrivate, CreatedAt, SyncStatus
                            )
                            VALUES (
                                @EventId, @TenantId, @UserId, @Title, @Description, @StartTime, @EndTime,
                                @Location, @IsAllDay, @Status, @ExternalEventId, @ExternalSystem,
                                @Operation, @AppointmentId, @IsPrivate, @CreatedAt, @SyncStatus
                            );";

                    command.Parameters.AddWithValue("@EventId", calendarEvent.EventId ?? string.Empty);
                    command.Parameters.AddWithValue("@TenantId", tenantId);
                    command.Parameters.AddWithValue("@UserId", calendarEvent.UserId);
                    command.Parameters.AddWithValue("@Title", calendarEvent.GraphEvent?.Subject ?? string.Empty);
                    command.Parameters.AddWithValue("@Description", (object?)calendarEvent.GraphEvent?.Body?.Content ?? DBNull.Value);
                    command.Parameters.AddWithValue("@StartTime", DateTime.TryParse(calendarEvent.GraphEvent?.Start?.DateTime, out var startTime) ? startTime : DateTime.UtcNow);
                    command.Parameters.AddWithValue("@EndTime", DateTime.TryParse(calendarEvent.GraphEvent?.End?.DateTime, out var endTime) ? endTime : DateTime.UtcNow.AddHours(1));
                    command.Parameters.AddWithValue("@Location", (object?)calendarEvent.GraphEvent?.Location?.DisplayName ?? DBNull.Value);
                    command.Parameters.AddWithValue("@IsAllDay", calendarEvent.GraphEvent?.IsAllDay ?? false);
                    command.Parameters.AddWithValue("@Status", (object?)calendarEvent.GraphEvent?.ShowAs?.ToString() ?? DBNull.Value);
                    command.Parameters.AddWithValue("@ExternalEventId", (object?)calendarEvent.EventId ?? DBNull.Value);
                    command.Parameters.AddWithValue("@ExternalSystem", "Microsoft Graph");
                    command.Parameters.AddWithValue("@Operation", calendarEvent.Operation.ToString());
                    command.Parameters.AddWithValue("@AppointmentId", DBNull.Value); // Not available in CalendarEventWrapper
                    command.Parameters.AddWithValue("@IsPrivate", calendarEvent.GraphEvent?.Sensitivity == Microsoft.Graph.Models.Sensitivity.Private);
                    command.Parameters.AddWithValue("@CreatedAt", calendarEvent.CreatedAt);
                    command.Parameters.AddWithValue("@ModifiedAt", DateTime.UtcNow);
                    command.Parameters.AddWithValue("@SyncStatus", calendarEvent.SyncStatus.ToString());

                    var rowsAffected = await command.ExecuteNonQueryAsync(cancellationToken);
                    return rowsAffected > 0;
                }
                catch (Exception ex) when (!(ex is DatabaseException))
                {
                    _logger.LogError(ex, "Error upserting calendar event {EventId} for tenant {TenantId}, user {UserId}",
                        calendarEvent.EventId, tenantId, calendarEvent.UserId);
                    throw new DatabaseException($"Error upserting calendar event: {ex.Message}", tenantId, calendarEvent.UserId.ToString(), true, ex);
                }
            },
            new Context("UpsertCalendarEvent"));
        }

        /// <summary>
        /// Updates the sync status of a calendar event
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="eventId">Event ID</param>
        /// <param name="status">Sync status</param>
        /// <param name="externalId">External event ID</param>
        /// <param name="errorMessage">Error message if any</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if successful</returns>
        public async Task<bool> UpdateCalendarEventSyncStatusAsync(
            string tenantId,
            string eventId,
            string status,
            string? externalId = null,
            string? errorMessage = null,
            CancellationToken cancellationToken = default)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    using var connection = await _connectionFactory.CreateConnectionAsync(tenantId);
                    using var command = _connectionFactory.CreateCommand(connection);

                    var sql = @"
                        UPDATE CalendarEvents
                        SET SyncStatus = @SyncStatus,
                            SyncErrorMessage = @SyncErrorMessage,
                            SyncAttempts = SyncAttempts + 1,
                            LastSyncAttempt = @LastSyncAttempt";

                    if (status == "Synced")
                    {
                        sql += ", SyncedAt = @SyncedAt";
                    }

                    if (!string.IsNullOrEmpty(externalId))
                    {
                        sql += ", ExternalEventId = @ExternalEventId, ExternalSystem = 'MicrosoftGraph'";
                    }

                    sql += " WHERE EventId = @EventId";

                    command.CommandText = sql;
                    command.Parameters.AddWithValue("@EventId", eventId);
                    command.Parameters.AddWithValue("@SyncStatus", status);
                    command.Parameters.AddWithValue("@SyncErrorMessage", (object?)errorMessage ?? DBNull.Value);
                    command.Parameters.AddWithValue("@LastSyncAttempt", DateTime.UtcNow);

                    if (status == "Synced")
                    {
                        command.Parameters.AddWithValue("@SyncedAt", DateTime.UtcNow);
                    }

                    if (!string.IsNullOrEmpty(externalId))
                    {
                        command.Parameters.AddWithValue("@ExternalEventId", externalId);
                    }

                    var rowsAffected = await command.ExecuteNonQueryAsync(cancellationToken);

                    if (rowsAffected == 0)
                    {
                        _logger.LogWarning("Calendar event {EventId} not found in tenant {TenantId} when updating sync status", eventId, tenantId);
                        return false;
                    }

                    return true;
                }
                catch (Exception ex) when (!(ex is DatabaseException))
                {
                    _logger.LogError(ex, "Error updating sync status for calendar event {EventId} in tenant {TenantId}", eventId, tenantId);
                    throw new DatabaseException($"Error updating calendar event sync status: {ex.Message}", tenantId, null, true, ex);
                }
            },
            new Context("UpdateCalendarEventSyncStatus"));
        }

        /// <summary>
        /// Updates the appointment processing status
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="appointmentId">Appointment ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="isProcessed">Whether processing is complete</param>
        /// <param name="status">Status message</param>
        /// <param name="errorMessage">Error message if any</param>
        /// <param name="graphResponseId">External event ID from Graph API</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if successful</returns>
        public async Task<bool> UpdateAppointmentProcessingStatusAsync(
            string tenantId,
            int appointmentId,
            int userId,
            bool isProcessed,
            string status,
            string? errorMessage = null,
            string? graphResponseId = null,
            CancellationToken cancellationToken = default)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    using var connection = await _connectionFactory.CreateConnectionAsync(tenantId);
                    using var command = _connectionFactory.CreateCommand(connection);

                    command.CommandText = @"
                        MERGE INTO AppointmentProcessingStatus AS target
                        USING (SELECT @AppointmentId, @TenantId, @UserId) AS source (AppointmentId, TenantId, UserId)
                        ON target.AppointmentId = source.AppointmentId AND target.TenantId = source.TenantId
                        WHEN MATCHED THEN
                            UPDATE SET
                                IsProcessed = @IsProcessed,
                                Status = @Status,
                                ErrorMessage = @ErrorMessage,
                                GraphResponseId = @GraphResponseId,
                                ProcessedAt = @ProcessedAt,
                                RetryCount = target.RetryCount + 1
                        WHEN NOT MATCHED THEN
                            INSERT (
                                AppointmentId, TenantId, UserId, IsProcessed, Status,
                                ErrorMessage, GraphResponseId, CreatedAt, ProcessedAt, RetryCount
                            )
                            VALUES (
                                @AppointmentId, @TenantId, @UserId, @IsProcessed, @Status,
                                @ErrorMessage, @GraphResponseId, @CreatedAt, @ProcessedAt, 1
                            );";

                    command.Parameters.AddWithValue("@AppointmentId", appointmentId);
                    command.Parameters.AddWithValue("@TenantId", tenantId);
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.Parameters.AddWithValue("@IsProcessed", isProcessed);
                    command.Parameters.AddWithValue("@Status", status);
                    command.Parameters.AddWithValue("@ErrorMessage", (object?)errorMessage ?? DBNull.Value);
                    command.Parameters.AddWithValue("@GraphResponseId", (object?)graphResponseId ?? DBNull.Value);
                    command.Parameters.AddWithValue("@CreatedAt", DateTime.UtcNow);
                    command.Parameters.AddWithValue("@ProcessedAt", isProcessed ? DateTime.UtcNow : (object)DBNull.Value);

                    var rowsAffected = await command.ExecuteNonQueryAsync(cancellationToken);
                    return rowsAffected > 0;
                }
                catch (Exception ex) when (!(ex is DatabaseException))
                {
                    _logger.LogError(ex, "Error updating processing status for appointment {AppointmentId} in tenant {TenantId}",
                        appointmentId, tenantId);
                    throw new DatabaseException($"Error updating appointment processing status: {ex.Message}", tenantId, null, true, ex);
                }
            },
            new Context("UpdateAppointmentProcessingStatus"));
        }

        /// <summary>
        /// Gets pending sync events for a user
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="batchSize">Maximum number of events to retrieve</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of pending calendar events</returns>
        public async Task<IEnumerable<CalendarEventWrapper>> GetPendingSyncEventsAsync(
            string tenantId,
            string userId,
            int batchSize,
            CancellationToken cancellationToken = default)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    using var connection = await _connectionFactory.CreateConnectionAsync(tenantId);
                    using var command = _connectionFactory.CreateCommand(connection);

                    command.CommandText = @"
                        SELECT TOP (@BatchSize)
                            EventId, TenantId, UserId, Title, Description, StartTime, EndTime,
                            Location, IsAllDay, Status, ExternalEventId, ExternalSystem,
                            Operation, AppointmentId, IsPrivate, CreatedAt, ModifiedAt,
                            SyncStatus, SyncErrorMessage, SyncAttempts, LastSyncAttempt, SyncedAt
                        FROM CalendarEvents
                        WHERE TenantId = @TenantId
                            AND UserId = @UserId
                            AND SyncStatus IN ('PendingSync', 'Failed')
                            AND SyncAttempts < @MaxAttempts
                        ORDER BY CreatedAt";

                    command.Parameters.AddWithValue("@TenantId", tenantId);
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.Parameters.AddWithValue("@BatchSize", batchSize);
                    command.Parameters.AddWithValue("@MaxAttempts", _options.MaxRetries);

                    var results = new List<CalendarEventWrapper>();

                    using var reader = await command.ExecuteReaderAsync(cancellationToken);

                    while (await reader.ReadAsync(cancellationToken))
                    {
                        var calendarEvent = ReadCalendarEvent(reader);
                        results.Add(calendarEvent);
                    }

                    return results;
                }
                catch (Exception ex) when (!(ex is DatabaseException))
                {
                    _logger.LogError(ex, "Error retrieving pending sync events for tenant {TenantId}, user {UserId}", tenantId, userId);
                    throw new DatabaseException($"Error retrieving pending sync events: {ex.Message}", tenantId, userId, true, ex);
                }
            },
            new Context("GetPendingSyncEvents"));
        }

        /// <summary>
        /// Reads a calendar event from a data reader
        /// </summary>
        private CalendarEventWrapper ReadCalendarEvent(SqlDataReader reader)
        {
            var startTime = reader.GetDateTime(5);
            var endTime = reader.GetDateTime(6);
            var isAllDay = reader.GetBoolean(8);
            var isPrivate = reader.GetBoolean(14);

            return new CalendarEventWrapper
            {
                EventId = reader.GetGuid(0).ToString(),
                TenantId = reader.GetInt32(1),
                UserId = reader.GetInt32(2),
                Operation = Enum.Parse<OperationType>(reader.GetString(12)),
                CreatedAt = reader.GetDateTime(15),
                ModifiedAt = reader.IsDBNull(16) ? null : reader.GetDateTime(16),
                SyncStatus = Enum.Parse<SyncStatus>(reader.GetString(17)),
                SyncErrorDetails = reader.IsDBNull(18) ? null : reader.GetString(18),
                SyncAttempts = reader.GetInt32(19),
                LastSyncedAt = reader.IsDBNull(21) ? null : reader.GetDateTime(21),
                GraphEvent = new Microsoft.Graph.Models.Event
                {
                    Id = reader.GetGuid(0).ToString(),
                    Subject = reader.GetString(3),
                    Body = new Microsoft.Graph.Models.ItemBody
                    {
                        Content = reader.IsDBNull(4) ? null : reader.GetString(4),
                        ContentType = Microsoft.Graph.Models.BodyType.Html
                    },
                    Start = new Microsoft.Graph.Models.DateTimeTimeZone
                    {
                        DateTime = startTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK"),
                        TimeZone = "UTC"
                    },
                    End = new Microsoft.Graph.Models.DateTimeTimeZone
                    {
                        DateTime = endTime.ToString("yyyy-MM-ddTHH:mm:ss.fffK"),
                        TimeZone = "UTC"
                    },
                    Location = new Microsoft.Graph.Models.Location
                    {
                        DisplayName = reader.IsDBNull(7) ? null : reader.GetString(7)
                    },
                    IsAllDay = isAllDay,
                    Sensitivity = isPrivate ? Microsoft.Graph.Models.Sensitivity.Private : Microsoft.Graph.Models.Sensitivity.Normal
                }
            };
        }
    }
}
