# Calendar Sync Code Examples

## 1. Models

### AppointmentMessage.cs

```csharp
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace WorkerService.Models;

public class AppointmentMessage
{
    public long? Version { get; set; }
    public string? Operation { get; set; } // "i" for insert, "u" for update, "d" for delete
    public string? Id { get; set; }
    public int? Columns { get; set; }
    public int? FirmId { get; set; }
    public AppointmentData? Data { get; set; }
    public string? Database { get; set; }

    public WorkerService.Models.Operation ToOperation()
    {
        return Operation?.ToLower() switch
        {
            "i" => WorkerService.Models.Operation.Create,
            "u" => WorkerService.Models.Operation.Update,
            "d" => WorkerService.Models.Operation.Delete,
            _ => WorkerService.Models.Operation.Create
        };
    }
}

public class AppointmentData
{
    public int? FirmId { get; set; }
    public int? UserId { get; set; }
    public string? Appo_Subject { get; set; }
    public int? MatterId { get; set; }
    public string? Appo_Description { get; set; }
    public string? Appo_Location { get; set; }
    public int? Appo_ActiveStatusId { get; set; }
    public int? Appo_Id { get; set; }
    public bool? Appo_IsPrivate { get; set; }
}

public class MessageKey
{
    public string FirmId { get; set; }
    public string Id { get; set; }
}

### AppointmentProcessingStatus.cs

```csharp
using System;

namespace WorkerService.Models;

public class AppointmentProcessingStatus
{
    public int Id { get; set; }
    public int AppointmentId { get; set; }
    public int FirmId { get; set; }
    public int UserId { get; set; }
    public bool IsProcessed { get; set; }
    public string Status { get; set; } // "Pending", "Processed", "Failed"
    public string ErrorMessage { get; set; }
    public string GraphResponseId { get; set; } // ID returned from Graph API
    public DateTime CreatedAt { get; set; }
    public DateTime? ProcessedAt { get; set; }
    public int RetryCount { get; set; }
}

### CalendarEvent.cs

```csharp
using System;
using System.Collections.Generic;

namespace WorkerService.Models;

public class CalendarEvent
{
    public Guid EventId { get; set; }
    public int TenantId { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string Location { get; set; }
    public string Organizer { get; set; }
    public List<string> Attendees { get; set; }
    public string RecurrenceRule { get; set; }
    public bool IsAllDay { get; set; }
    public string Status { get; set; }
    public string ExternalId { get; set; }
    public string ExternalSystem { get; set; }
    public Operation Operation { get; set; }
    public int UserId { get; set; } // User who owns this calendar event
}

public enum Operation
{
    Create,
    Update,
    Delete
}

/// <summary>
/// Represents a user's sync configuration including access tokens
/// </summary>
public class UserSyncConfig
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public int TenantId { get; set; }
    public string Email { get; set; }
    public string Security { get; set; } // Contains AccessToken
    public string RefreshToken { get; set; }
    public DateTime TokenExpiry { get; set; }
    public bool IsValid { get; set; } = true;
    public DateTime? LastSyncTime { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}
```

## 2. Configuration

### Options.cs

```csharp
using System.ComponentModel.DataAnnotations;
using Confluent.Kafka;

namespace WorkerService;

public record CalendarSyncOptions
{
    [Required] public int BatchSize { get; init; } = 100;
    [Required] public int ChannelCapacity { get; init; } = 10000;
    [Required] public int Concurrency { get; init; } = 4;
    [Required] public int CacheExpirationMin { get; init; } = 15;
    [Required] public int ConsumeTimeoutMs { get; init; } = 10000;
    [Required] public double MaxBackoffDelayMs { get; init; } = 5000;
}

public record KafkaOptions
{
    [Required, MinLength(3)] public required string BootstrapServers { get; init; }
    [Required] public AutoOffsetReset AutoOffsetReset { get; init; } = AutoOffsetReset.Earliest;
    [Required] public int FetchWaitMaxMs { get; init; }
    [Required] public int MaxPollIntervalMs { get; init; }
    [Required] public int AutoCommitIntervalMs { get; init; }
    public SaslMechanism? SaslMechanism { get; init; }
    public string? SaslUsername { get; init; }
    public string? SaslPassword { get; init; }
    public SecurityProtocol? SecurityProtocol { get; init; }
}

public record SystemDbOptions
{
    [Required, MinLength(3)] public required string ConnectionString { get; init; }
    [Required] public int MaxRetryCount { get; init; }
    [Required] public TimeSpan MaxRetryDelay { get; init; }
    public int[]? ErrorNumbersToAdd { get; init; }
}
```

## 3. Message Bus

### MessageBus.cs

```csharp
using System;
using System.Threading;
using System.Threading.Channels;
using System.Threading.Tasks;
using Confluent.Kafka;
using Microsoft.Extensions.Options;
using Serilog;
using WorkerService.Factories;
using WorkerService.Models;

namespace WorkerService.Infrastructure;

public class MessageBus
{
    private readonly Channel<ConsumeResult<string, CalendarEvent>> _channel;
    private IConsumer<string, CalendarEvent> _consumer = null!;
    private readonly CalendarSyncOptions _options;
    private readonly ConsumerFactory<string, CalendarEvent> _factory;

    public MessageBus(IOptionsMonitor<CalendarSyncOptions> options, ConsumerFactory<string, CalendarEvent> factory)
    {
        _options = options.CurrentValue;
        _factory = factory;
        _channel = Channel.CreateBounded<ConsumeResult<string, CalendarEvent>>(
            new BoundedChannelOptions(_options.ChannelCapacity)
            {
                FullMode = BoundedChannelFullMode.Wait,
                SingleWriter = true,
                SingleReader = false
            });
    }

    public IAsyncEnumerable<ConsumeResult<string, CalendarEvent>> SubscribeAsync(CancellationToken cancellation)
        => _channel.Reader.ReadAllAsync(cancellation);

    public void StoreOffset(ConsumeResult<string, CalendarEvent> message) => _consumer.StoreOffset(message);

    public async ValueTask StartConsumingAsync(string consumerGroup, string topic, CancellationToken cancellation)
    {
        _consumer = _factory.CreateConsumer(consumerGroup);

        Log.Information("Subscribing {ConsumerGroup} to {Topic}", consumerGroup, topic);
        _consumer.Subscribe(topic);

        while (!cancellation.IsCancellationRequested)
        {
            try
            {
                var result = _consumer.Consume(_options.ConsumeTimeoutMs);

                if (result?.Message is null) continue;
                if (result.IsPartitionEOF) continue;
                if (cancellation.IsCancellationRequested) break;

                await ApplyBackpressureAsync(cancellation);
                await _channel.Writer.WriteAsync(result, cancellation);
            }
            catch (Exception ex)
            {
                await HandleConsumeExceptionAsync(ex, consumerGroup, topic, cancellation);
            }
        }

        _consumer.Close();
        _channel.Writer.Complete();
    }

    private async ValueTask ApplyBackpressureAsync(CancellationToken cancellation)
    {
        var capacity = _options.ChannelCapacity;
        var maxBackoffDelayMs = _options.MaxBackoffDelayMs;
        var currentCount = _channel.Reader.CanCount ? _channel.Reader.Count : 0;

        if (currentCount <= 0) return;

        var ratio = currentCount / (double)capacity;
        var delayMs = (int)(ratio * maxBackoffDelayMs);

        if (delayMs <= 0) return;

        await Task.Delay(delayMs, cancellation);
    }

    private Task HandleConsumeExceptionAsync(Exception ex, string consumerGroup, string topic, CancellationToken cancellation)
    {
        // Handle different types of exceptions
        return Task.CompletedTask;
    }
}
```

## 4. Consumer Service

### ConsumerService.cs

```csharp
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Confluent.Kafka;
using Microsoft.Extensions.Options;
using Serilog;
using WorkerService.Factories;
using WorkerService.Infrastructure;
using WorkerService.Models;

namespace WorkerService.Services;

public class ConsumerService
{
    private readonly ConsumerFactory<string, CalendarEvent> _factory;
    private readonly CalendarSyncOptions _options;

    public ConsumerService(ConsumerFactory<string, CalendarEvent> factory, IOptionsMonitor<CalendarSyncOptions> options)
    {
        _factory = factory;
        _options = options.CurrentValue;
    }

    public async ValueTask ConsumeAsync(
        Func<IAsyncEnumerable<ConsumeResult<string, CalendarEvent>>, CancellationToken, ValueTask> handleAsync,
        string topic,
        string consumerGroup,
        CancellationToken cancellation)
    {
        MessageBus bus = new(new OptionsMonitorWrapper<CalendarSyncOptions>(_options), _factory);
        var processingTasks = CreateProcessingTasks(bus, handleAsync, cancellation);
        await bus.StartConsumingAsync(consumerGroup, topic, cancellation);
        await Task.WhenAll(processingTasks);
    }

    private List<Task> CreateProcessingTasks(
        MessageBus bus,
        Func<IAsyncEnumerable<ConsumeResult<string, CalendarEvent>>, CancellationToken, ValueTask> handleAsync,
        CancellationToken cancellation)
    {
        List<Task> tasks = [];

        for (var i = 0; i < _options.Concurrency; i++)
            tasks.Add(Task.Run(async () =>
                await handleAsync(bus.SubscribeAsync(cancellation), cancellation),
                cancellation));

        return tasks;
    }
}

// Helper class for options
internal class OptionsMonitorWrapper<T> : IOptionsMonitor<T> where T : class
{
    private readonly T _currentValue;

    public OptionsMonitorWrapper(T currentValue)
    {
        _currentValue = currentValue;
    }

    public T CurrentValue => _currentValue;
    public T Get(string name) => _currentValue;
    public IDisposable OnChange(Action<T, string> listener) => new DummyDisposable();

    private class DummyDisposable : IDisposable
    {
        public void Dispose() { }
    }
}
```

### AvroDeserializer.cs

```csharp
using System;
using System.Collections.Generic;
using Avro;
using Avro.Generic;
using Confluent.Kafka;
using Confluent.SchemaRegistry;
using Serilog;
using WorkerService.Models;

namespace WorkerService.Serialization
{
    public class AvroDeserializer<T> : IDeserializer<T> where T : class
    {
        private readonly ISchemaRegistryClient _schemaRegistryClient;

        public AvroDeserializer(ISchemaRegistryClient schemaRegistryClient)
        {
            _schemaRegistryClient = schemaRegistryClient;
        }

        public T Deserialize(ReadOnlySpan<byte> data, bool isNull, SerializationContext context)
        {
            if (isNull)
            {
                return null;
            }

            try
            {
                // The Avro serialized data is preceded by a "magic byte" (0) and the schema id (4 bytes)
                var magicByte = data[0];
                var schemaId = BitConverter.ToInt32(data.Slice(1, 4));
                var schema = _schemaRegistryClient.GetSchemaAsync(schemaId).GetAwaiter().GetResult();

                var reader = new GenericDatumReader<GenericRecord>(Schema.Parse(schema.SchemaString));
                var decoder = new BinaryDecoder(data.Slice(5).ToArray());
                var record = reader.Read(null, decoder);

                if (typeof(T) == typeof(AppointmentMessage))
                {
                    return DeserializeAppointmentMessage(record) as T;
                }
                else if (typeof(T) == typeof(MessageKey))
                {
                    return DeserializeMessageKey(record) as T;
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deserializing Avro message");
                return null;
            }
        }

        private AppointmentMessage DeserializeAppointmentMessage(GenericRecord record)
        {
            var appointmentMessage = new AppointmentMessage
            {
                Version = GetNullableValue<long>(record, "Version"),
                Operation = GetNullableValue<string>(record, "Operation"),
                Id = GetNullableValue<string>(record, "Id"),
                Columns = GetNullableValue<int>(record, "Columns"),
                FirmId = GetNullableValue<int>(record, "FirmId"),
                Database = GetNullableValue<string>(record, "Database")
            };

            var dataRecord = record.GetField("Data") as GenericRecord;
            if (dataRecord != null)
            {
                appointmentMessage.Data = new AppointmentData
                {
                    FirmId = GetNullableValue<int>(dataRecord, "firmId"),
                    UserId = GetNullableValue<int>(dataRecord, "userId"),
                    Appo_Subject = GetNullableValue<string>(dataRecord, "appo_subject"),
                    MatterId = GetNullableValue<int>(dataRecord, "matterId"),
                    Appo_Description = GetNullableValue<string>(dataRecord, "appo_description"),
                    Appo_Location = GetNullableValue<string>(dataRecord, "appo_location"),
                    Appo_ActiveStatusId = GetNullableValue<int>(dataRecord, "appo_activestatusid"),
                    Appo_Id = GetNullableValue<int>(dataRecord, "appo_id"),
                    Appo_IsPrivate = GetNullableValue<bool>(dataRecord, "appo_IsPrivate")
                };
            }

            return appointmentMessage;
        }

        private MessageKey DeserializeMessageKey(GenericRecord record)
        {
            return new MessageKey
            {
                FirmId = GetValue<string>(record, "FirmId"),
                Id = GetValue<string>(record, "Id")
            };
        }

        private static T GetValue<T>(GenericRecord record, string fieldName)
        {
            if (record.TryGetValue(fieldName, out var value))
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            return default;
        }

        private static T? GetNullableValue<T>(GenericRecord record, string fieldName) where T : struct
        {
            if (record.TryGetValue(fieldName, out var value) && value != null)
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            return null;
        }

        private static string GetNullableValue<T>(GenericRecord record, string fieldName) where T : class
        {
            if (record.TryGetValue(fieldName, out var value) && value != null)
            {
                return value.ToString();
            }
            return null;
        }
    }
}

### GraphService.cs

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using Serilog;
using WorkerService.Models;

namespace WorkerService.Services;

public class GraphService
{
    private readonly HttpClient _httpClient;
    private readonly TokenService _tokenService;
    private readonly JsonSerializerOptions _jsonOptions;

    public GraphService(TokenService tokenService, IHttpClientFactory httpClientFactory)
    {
        _tokenService = tokenService;
        _httpClient = httpClientFactory.CreateClient("GraphApi");
        _httpClient.BaseAddress = new Uri("https://graph.microsoft.com/v1.0/");
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<List<string>> BatchCreateEventsAsync(
        List<CalendarEvent> events,
        int userId,
        int tenantId,
        CancellationToken cancellationToken)
    {
        var accessToken = await _tokenService.GetAccessTokenAsync(userId, tenantId, cancellationToken);
        if (string.IsNullOrEmpty(accessToken))
        {
            throw new MsalUiRequiredException(
                "TokenNotFound",
                $"Access token not found for user {userId} in tenant {tenantId}");
        }

        // Create batch request with a maximum of 20 operations
        var batchRequests = new List<BatchRequest>();
        var responses = new List<string>();
        
        foreach (var @event in events)
        {
            var eventPayload = new
            {
                subject = @event.Title,
                body = new
                {
                    contentType = "HTML",
                    content = @event.Description
                },
                start = new
                {
                    dateTime = @event.StartTime.ToString("o"),
                    timeZone = "UTC"
                },
                end = new
                {
                    dateTime = @event.EndTime.ToString("o"),
                    timeZone = "UTC"
                },
                location = new
                {
                    displayName = @event.Location
                }
            };

            var operation = @event.Operation switch
            {
                Operation.Create => "POST",
                Operation.Update => "PATCH",
                Operation.Delete => "DELETE",
                _ => "POST"
            };

            string url = "me/events";
            if (@event.Operation == Operation.Update || @event.Operation == Operation.Delete)
            {
                // For update and delete, we need the event ID
                if (!string.IsNullOrEmpty(@event.ExternalId))
                {
                    url = $"me/events/{@event.ExternalId}";
                }
                else
                {
                    // Skip if we don't have an external ID for update/delete operations
                    continue;
                }
            }

            batchRequests.Add(new BatchRequest
            {
                Id = $"{@event.EventId}",
                Method = operation,
                Url = url,
                Body = operation != "DELETE" ? eventPayload : null
            });
        }

        // Process in batches of 20
        for (int i = 0; i < batchRequests.Count; i += 20)
        {
            var batchSlice = batchRequests.Skip(i).Take(20).ToList();
            var batchResponse = await ExecuteBatchRequestAsync(batchSlice, accessToken, cancellationToken);
            
            if (batchResponse != null)
            {
                responses.AddRange(batchResponse);
            }
        }

        return responses;
    }

    private async Task<List<string>> ExecuteBatchRequestAsync(
        List<BatchRequest> requests, 
        string accessToken,
        CancellationToken cancellationToken)
    {
        var batchPayload = new
        {
            requests = requests
        };

        using var request = new HttpRequestMessage(HttpMethod.Post, "$batch");
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
        request.Content = new StringContent(
            JsonSerializer.Serialize(batchPayload, _jsonOptions),
            Encoding.UTF8,
            "application/json");

        try
        {
            using var response = await _httpClient.SendAsync(request, cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync(cancellationToken);
                Log.Error("Graph API batch request failed: {Error}", error);
                return null;
            }

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var batchResponse = JsonSerializer.Deserialize<BatchResponse>(content, _jsonOptions);
            
            return batchResponse?.Responses
                .Where(r => r.Status >= 200 && r.Status < 300)
                .Select(r => JsonSerializer.Deserialize<GraphEventResponse>(r.Body.ToString(), _jsonOptions)?.Id)
                .Where(id => !string.IsNullOrEmpty(id))
                .ToList();
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error executing Graph API batch request");
            return null;
        }
    }

    private class BatchRequest
    {
        public string Id { get; set; }
        public string Method { get; set; }
        public string Url { get; set; }
        public object Body { get; set; }
    }

    private class BatchResponse
    {
        public List<BatchResponseItem> Responses { get; set; } = new();
    }

    private class BatchResponseItem
    {
        public string Id { get; set; }
        public int Status { get; set; }
        public object Body { get; set; }
    }

    private class GraphEventResponse
    {
        public string Id { get; set; }
    }
}

### TokenService.cs

```csharp
using System;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Identity.Client;
using Serilog;
using WorkerService.Contexts;
using WorkerService.Models;

namespace WorkerService.Services;

public class TokenService
{
    private readonly TenantResolver _resolver;
    private readonly IMemoryCache _cache;

    public TokenService(TenantResolver resolver, IMemoryCache cache)
    {
        _resolver = resolver;
        _cache = cache;
    }

    /// <summary>
    /// Gets the access token for a specific user
    /// </summary>
    public async Task<string> GetAccessTokenAsync(int userId, int tenantId, CancellationToken token)
    {
        var cacheKey = $"token:{userId}:{tenantId}";

        return await _cache.GetOrCreateAsync(cacheKey, async entry =>
        {
            entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(10); // Cache for 10 minutes

            try
            {
                var userConfig = await GetUserSyncConfigAsync(userId, tenantId, token);

                if (userConfig == null || !userConfig.IsValid)
                {
                    Log.Warning("No valid token configuration found for user {UserId} in tenant {TenantId}", userId, tenantId);
                    return null;
                }

                // Extract access token from Security field
                if (!string.IsNullOrEmpty(userConfig.Security))
                {
                    try
                    {
                        // Assuming Security is a JSON string containing the access token
                        var securityData = JsonSerializer.Deserialize<Dictionary<string, string>>(userConfig.Security);
                        if (securityData != null && securityData.TryGetValue("accessToken", out var accessToken))
                        {
                            return accessToken;
                        }
                    }
                    catch (JsonException ex)
                    {
                        Log.Error(ex, "Error parsing Security field for user {UserId}", userId);
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error retrieving access token for user {UserId}", userId);
                return null;
            }
        });
    }

    /// <summary>
    /// Marks a user's token as invalid in the database
    /// </summary>
    public async Task InvalidateTokenAsync(int userId, int tenantId, CancellationToken token)
    {
        try
        {
            var connection = await _resolver.GetTenantConnectionAsync(tenantId.ToString(), token);
            if (connection == null) return;

            await connection.OpenAsync(token);

            using var command = connection.CreateCommand();
            command.CommandText = @"
                UPDATE T_UserSyncConfig
                SET IsValid = 0, UpdatedAt = GETUTCDATE()
                WHERE UserId = @UserId AND TenantId = @TenantId";

            command.Parameters.AddWithValue("@UserId", userId);
            command.Parameters.AddWithValue("@TenantId", tenantId);

            await command.ExecuteNonQueryAsync(token);

            // Remove from cache
            var cacheKey = $"token:{userId}:{tenantId}";
            _cache.Remove(cacheKey);

            Log.Information("Token invalidated for user {UserId} in tenant {TenantId}", userId, tenantId);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error invalidating token for user {UserId} in tenant {TenantId}", userId, tenantId);
        }
    }

    /// <summary>
    /// Retrieves the user sync configuration from the database
    /// </summary>
    private async Task<UserSyncConfig> GetUserSyncConfigAsync(int userId, int tenantId, CancellationToken token)
    {
        var connection = await _resolver.GetTenantConnectionAsync(tenantId.ToString(), token);
        if (connection == null) return null;

        await connection.OpenAsync(token);

        using var command = connection.CreateCommand();
        command.CommandText = @"
            SELECT Id, UserId, TenantId, Email, Security, RefreshToken,
                   TokenExpiry, IsValid, LastSyncTime, CreatedAt, UpdatedAt
            FROM T_UserSyncConfig
            WHERE UserId = @UserId AND TenantId = @TenantId AND IsValid = 1";

        command.Parameters.AddWithValue("@UserId", userId);
        command.Parameters.AddWithValue("@TenantId", tenantId);

        using var reader = await command.ExecuteReaderAsync(token);

        if (await reader.ReadAsync(token))
        {
            return new UserSyncConfig
            {
                Id = reader.GetInt32(0),
                UserId = reader.GetInt32(1),
                TenantId = reader.GetInt32(2),
                Email = reader.GetString(3),
                Security = reader.IsDBNull(4) ? null : reader.GetString(4),
                RefreshToken = reader.IsDBNull(5) ? null : reader.GetString(5),
                TokenExpiry = reader.IsDBNull(6) ? DateTime.MinValue : reader.GetDateTime(6),
                IsValid = reader.GetBoolean(7),
                LastSyncTime = reader.IsDBNull(8) ? null : reader.GetDateTime(8),
                CreatedAt = reader.GetDateTime(9),
                UpdatedAt = reader.GetDateTime(10)
            };
        }

        return null;
    }
}
```

## 5. Calendar Sync Service

### CalendarSyncService.cs

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Microsoft.Identity.Client;
using Serilog;
using WorkerService.Gateways;
using WorkerService.Models;

namespace WorkerService.Services;

public class CalendarSyncService
{
    private readonly SqlGateway _sqlGateway;
    private readonly CalendarSyncOptions _options;
    private readonly TokenService _tokenService;

    public CalendarSyncService(
        SqlGateway sqlGateway,
        TokenService tokenService,
        IOptionsMonitor<CalendarSyncOptions> options)
    {
        _sqlGateway = sqlGateway;
        _tokenService = tokenService;
        _options = options.CurrentValue;
    }

    public async Task ProcessBatchAsync(List<CalendarEvent> events, CancellationToken token)
    {
        if (events.Count == 0) return;

        try
        {
            // Group events by user to ensure we have the right access token for each user
            var eventsByUser = events.GroupBy(e => e.UserId);

            foreach (var userGroup in eventsByUser)
            {
                var userId = userGroup.Key;
                var userEvents = userGroup.ToList();

                try
                {
                    // Get access token for this user
                    var accessToken = await _tokenService.GetAccessTokenAsync(userId, userEvents.First().TenantId, token);

                    if (string.IsNullOrEmpty(accessToken))
                    {
                        Log.Warning("Skipping {Count} events for user {UserId} due to missing access token",
                            userEvents.Count, userId);
                        continue;
                    }

                    // Group events by operation type
                    var createEvents = userEvents.Where(e => e.Operation == Operation.Create).ToList();
                    var updateEvents = userEvents.Where(e => e.Operation == Operation.Update).ToList();
                    var deleteEvents = userEvents.Where(e => e.Operation == Operation.Delete).ToList();

                    // Process each batch in parallel
                    var tasks = new List<Task>();

                    if (createEvents.Any())
                        tasks.Add(_sqlGateway.BatchCreateAsync(createEvents, token));

                    if (updateEvents.Any())
                        tasks.Add(_sqlGateway.BatchUpdateAsync(updateEvents, token));

                    if (deleteEvents.Any())
                        tasks.Add(_sqlGateway.BatchDeleteAsync(deleteEvents, token));

                    await Task.WhenAll(tasks);

                    Log.Information("Processed batch of {Count} events for user {UserId}: {Creates} creates, {Updates} updates, {Deletes} deletes",
                        userEvents.Count, userId, createEvents.Count, updateEvents.Count, deleteEvents.Count);
                }
                catch (MsalUiRequiredException ex)
                {
                    // Handle authentication errors by marking the token as invalid
                    await _tokenService.InvalidateTokenAsync(userId, userEvents.First().TenantId, token);

                    Log.Error(ex, "Authentication error for user {UserId}. Token has been invalidated.", userId);
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "Error processing batch of {Count} events for user {UserId}", userEvents.Count, userId);
                }
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Error processing batch of {Count} events", events.Count);
            throw;
        }
    }
}
```

## 6. SQL Gateway

### SqlGateway.cs

```csharp
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;
using Serilog;
using WorkerService.Contexts;
using WorkerService.Models;

namespace WorkerService.Gateways;

public class SqlGateway
{
    private readonly TenantResolver _resolver;

    public SqlGateway(TenantResolver resolver)
    {
        _resolver = resolver;
    }

    public async Task BatchCreateAsync(List<CalendarEvent> events, CancellationToken token)
    {
        // Group events by tenant/database
        var eventsByTenant = events.GroupBy(e => e.TenantId);

        foreach (var tenantGroup in eventsByTenant)
        {
            var connection = await _resolver.GetTenantConnectionAsync(tenantGroup.Key.ToString(), token);
            if (connection == null) continue;

            try
            {
                await connection.OpenAsync(token);

                using var transaction = connection.BeginTransaction();
                using var bulkCopy = new SqlBulkCopy(connection, SqlBulkCopyOptions.Default, transaction);

                bulkCopy.DestinationTableName = "CalendarEvents";
                bulkCopy.BatchSize = events.Count;

                // Map columns
                bulkCopy.ColumnMappings.Add("EventId", "EventId");
                bulkCopy.ColumnMappings.Add("TenantId", "TenantId");
                bulkCopy.ColumnMappings.Add("Title", "Title");
                bulkCopy.ColumnMappings.Add("Description", "Description");
                bulkCopy.ColumnMappings.Add("StartTime", "StartTime");
                bulkCopy.ColumnMappings.Add("EndTime", "EndTime");
                bulkCopy.ColumnMappings.Add("Location", "Location");
                bulkCopy.ColumnMappings.Add("Organizer", "Organizer");
                bulkCopy.ColumnMappings.Add("Attendees", "Attendees");
                bulkCopy.ColumnMappings.Add("RecurrenceRule", "RecurrenceRule");
                bulkCopy.ColumnMappings.Add("IsAllDay", "IsAllDay");
                bulkCopy.ColumnMappings.Add("Status", "Status");
                bulkCopy.ColumnMappings.Add("ExternalId", "ExternalId");
                bulkCopy.ColumnMappings.Add("ExternalSystem", "ExternalSystem");

                var dataTable = CreateDataTable(tenantGroup.ToList());
                await bulkCopy.WriteToServerAsync(dataTable, token);

                transaction.Commit();

                Log.Information("Created {Count} calendar events for tenant {TenantId}",
                    tenantGroup.Count(), tenantGroup.Key);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating calendar events for tenant {TenantId}", tenantGroup.Key);
                throw;
            }
            finally
            {
                if (connection.State == ConnectionState.Open)
                    await connection.CloseAsync();
            }
        }
    }

    public async Task BatchUpdateAsync(List<CalendarEvent> events, CancellationToken token)
    {
        // Implementation similar to BatchCreateAsync but using stored procedure
    }

    public async Task BatchDeleteAsync(List<CalendarEvent> events, CancellationToken token)
    {
        // Implementation similar to BatchCreateAsync but with delete logic
    }

    private DataTable CreateDataTable(List<CalendarEvent> events)
    {
        var table = new DataTable();

        // Add columns
        table.Columns.Add("EventId", typeof(Guid));
        table.Columns.Add("TenantId", typeof(int));
        table.Columns.Add("Title", typeof(string));
        table.Columns.Add("Description", typeof(string));
        table.Columns.Add("StartTime", typeof(DateTime));
        table.Columns.Add("EndTime", typeof(DateTime));
        table.Columns.Add("Location", typeof(string));
        table.Columns.Add("Organizer", typeof(string));
        table.Columns.Add("Attendees", typeof(string));
        table.Columns.Add("RecurrenceRule", typeof(string));
        table.Columns.Add("IsAllDay", typeof(bool));
        table.Columns.Add("Status", typeof(string));
        table.Columns.Add("ExternalId", typeof(string));
        table.Columns.Add("ExternalSystem", typeof(string));

        // Add rows
        foreach (var evt in events)
        {
            var row = table.NewRow();
            row["EventId"] = evt.EventId;
            row["TenantId"] = evt.TenantId;
            row["Title"] = evt.Title;
            row["Description"] = evt.Description ?? (object)DBNull.Value;
            row["StartTime"] = evt.StartTime;
            row["EndTime"] = evt.EndTime;
            row["Location"] = evt.Location ?? (object)DBNull.Value;
            row["Organizer"] = evt.Organizer ?? (object)DBNull.Value;
            row["Attendees"] = evt.Attendees != null ? JsonSerializer.Serialize(evt.Attendees) : (object)DBNull.Value;
            row["RecurrenceRule"] = evt.RecurrenceRule ?? (object)DBNull.Value;
            row["IsAllDay"] = evt.IsAllDay;
            row["Status"] = evt.Status ?? (object)DBNull.Value;
            row["ExternalId"] = evt.ExternalId ?? (object)DBNull.Value;
            row["ExternalSystem"] = evt.ExternalSystem ?? (object)DBNull.Value;

            table.Rows.Add(row);
        }

        return table;
    }
}
```
