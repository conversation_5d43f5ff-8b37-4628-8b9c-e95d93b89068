using System;
using System.Reflection;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Validation
{
    /// <summary>
    /// Extension methods for registering validators with DI container
    /// </summary>
    public static class ValidationExtensions
    {
        /// <summary>
        /// Adds all validators in the specified assembly to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddAppointmentsConsumerValidation(this IServiceCollection services)
        {
            // Register all validators in this assembly
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

            return services;
        }

        /// <summary>
        /// Validates an instance of T using the registered validators and throws an exception if validation fails
        /// </summary>
        /// <typeparam name="T">Type of object to validate</typeparam>
        /// <param name="model">The object to validate</param>
        /// <param name="serviceProvider">Service provider to resolve the validator</param>
        /// <exception cref="ValidationException">Thrown when the model fails validation</exception>
        public static void ValidateAndThrow<T>(this T model, IServiceProvider serviceProvider)
        {
            var validator = serviceProvider.GetRequiredService<IValidator<T>>();
            validator.ValidateAndThrow(model);
        }

        /// <summary>
        /// Validates an instance of T using the registered validators
        /// </summary>
        /// <typeparam name="T">Type of object to validate</typeparam>
        /// <param name="model">The object to validate</param>
        /// <param name="serviceProvider">Service provider to resolve the validator</param>
        /// <returns>Validation result</returns>
        public static FluentValidation.Results.ValidationResult Validate<T>(this T model, IServiceProvider serviceProvider)
        {
            var validator = serviceProvider.GetRequiredService<IValidator<T>>();
            return validator.Validate(model);
        }
    }
}
