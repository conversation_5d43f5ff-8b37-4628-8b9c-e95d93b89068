using System.ComponentModel.DataAnnotations;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration
{
    public class DatabaseOptions
    {
        [Required]
        public string SystemDbConnectionString { get; set; } = string.Empty;
        
        public string SystemConnectionString 
        { 
            get => SystemDbConnectionString;
            set => SystemDbConnectionString = value;
        }
        
        [Range(0, 10)]
        public int MaxRetryCount { get; set; } = 3;
        
        public int MaxRetries
        {
            get => MaxRetryCount;
            set => MaxRetryCount = value;
        }
        
        [Range(100, 10000)]
        public int RetryBaseDelayMs { get; set; } = 500;
        
        public int RetryDelaySeconds
        {
            get => RetryBaseDelayMs / 1000;
            set => RetryBaseDelayMs = value * 1000;
        }
        
        [Range(1000, 60000)]
        public int MaxRetryDelayMs { get; set; } = 10000;
        
        public int[] TransientErrorNumbers { get; set; } = new[]
        {
            -2,
            4060,
            40197,
            40501,
            40613,
            49918,
            4221,
            
            1205,
            
            233,
            8645,
            8651,
        };
        
        [Range(30, 600)]
        public int CommandTimeoutSeconds { get; set; } = 60;
        
        [Range(15, 300)]
        public int ConnectionTimeoutSeconds { get; set; } = 30;
        
        public bool EnableConnectionPooling { get; set; } = true;
        
        [Range(1, 100)]
        public int MinPoolSize { get; set; } = 5;
        
        [Range(10, 1000)]
        public int MaxPoolSize { get; set; } = 100;
    }
}
