using System;
using System.Collections.Concurrent;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Exceptions;
using Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Resilience;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;

namespace Caret.CaretLegal.AppointmentsConsumer.Consumers.AppointmentsWorker.Contexts
{
    /// <summary>
    /// Factory for creating tenant-specific database connections
    /// </summary>
    public class TenantConnectionFactory
    {
        private readonly ILogger<TenantConnectionFactory> _logger;
        private readonly SystemDbContext _systemDbContext;
        private readonly DatabaseOptions _dbOptions;
        private readonly ResiliencePolicies _resilience;
        
        // Cache of tenant connection strings to avoid repeated lookups
        private readonly ConcurrentDictionary<string, string> _connectionStringCache = new();
        
        /// <summary>
        /// Creates a new instance of TenantConnectionFactory
        /// </summary>
        public TenantConnectionFactory(
            ILogger<TenantConnectionFactory> logger,
            SystemDbContext systemDbContext,
            IOptions<DatabaseOptions> options,
            ResiliencePolicies resilience)
        {
            _logger = logger;
            _systemDbContext = systemDbContext;
            _dbOptions = options.Value;
            _resilience = resilience;
        }
        
        /// <summary>
        /// Gets a connection string for the specified tenant
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Connection string for the tenant's database</returns>
        public async Task<string> GetConnectionStringAsync(string tenantId)
        {
            // Check cache first
            if (_connectionStringCache.TryGetValue(tenantId, out var connectionString))
            {
                return connectionString;
            }
            
            // Query system database for the tenant connection info
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                const string sql = @"
                    SELECT ConnectionString 
                    FROM Tenants 
                    WHERE TenantId = @TenantId AND IsActive = 1";
                
                var parameter = new SqlParameter("@TenantId", tenantId);
                
                var result = await _systemDbContext.ExecuteScalarAsync<string>(sql, parameter);
                
                if (string.IsNullOrEmpty(result))
                {
                    _logger.LogError("No connection string found for tenant {TenantId}", tenantId);
                    throw new DatabaseException($"Tenant {tenantId} not found or inactive", tenantId);
                }
                
                // Cache the result
                _connectionStringCache[tenantId] = result;
                
                return result;
            },
            new Context("GetTenantConnectionString"));
        }
        
        /// <summary>
        /// Creates a new connection for the specified tenant
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>An open database connection for the tenant</returns>
        public async Task<SqlConnection> CreateConnectionAsync(string tenantId)
        {
            return await _resilience.DatabasePolicy.ExecuteAsync(async (ctx) =>
            {
                try
                {
                    var connectionString = await GetConnectionStringAsync(tenantId);
                    
                    // Apply connection timeout and pooling settings
                    var builder = new SqlConnectionStringBuilder(connectionString)
                    {
                        ConnectTimeout = _dbOptions.ConnectionTimeoutSeconds,
                        Pooling = _dbOptions.EnableConnectionPooling,
                        MinPoolSize = _dbOptions.MinPoolSize,
                        MaxPoolSize = _dbOptions.MaxPoolSize
                    };
                    
                    var connection = new SqlConnection(builder.ConnectionString);
                    await connection.OpenAsync();
                    return connection;
                }
                catch (DatabaseException)
                {
                    // Rethrow database exceptions
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to create connection for tenant {TenantId}", tenantId);
                    throw new DatabaseException($"Failed to create connection for tenant {tenantId}", tenantId, isTransient: true, innerException: ex);
                }
            },
            new Context("CreateTenantConnection"));
        }
        
        /// <summary>
        /// Creates a command with the specified timeout for a tenant connection
        /// </summary>
        /// <param name="connection">SQL connection</param>
        /// <returns>SQL command with configured timeout</returns>
        public SqlCommand CreateCommand(SqlConnection connection)
        {
            var command = connection.CreateCommand();
            command.CommandTimeout = _dbOptions.CommandTimeoutSeconds;
            return command;
        }
        
        /// <summary>
        /// Invalidates the cached connection string for a tenant
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        public void InvalidateCache(string tenantId)
        {
            _connectionStringCache.TryRemove(tenantId, out _);
            _logger.LogInformation("Invalidated connection string cache for tenant {TenantId}", tenantId);
        }
    }
}
