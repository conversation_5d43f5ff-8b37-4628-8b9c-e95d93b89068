using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Confluent.Kafka;
using Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Configuration;
using Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Models;
using Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Services;
using Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Serialization;
using Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Infrastructure;
using System.Diagnostics;
using System.Threading.Channels;
using System.Collections.Concurrent;

namespace Caret.CaretLegal.AppointmentsConsumer.src.Consumers.AppointmentsWorker.Services
{
    /// <summary>
    /// Main hosted service for processing appointment messages from Kafka.
    /// Handles message consumption, deserialization, batching, and processing with
    /// comprehensive error handling, telemetry, and graceful shutdown.
    /// </summary>
    public class AppointmentsHostedService : BackgroundService
    {
        private readonly ILogger<AppointmentsHostedService> _logger;
        private readonly KafkaOptions _kafkaOptions;
        private readonly ApplicationOptions _appOptions;
        private readonly BatchProcessor _batchProcessor;
        private readonly AvroMessageDeserializer _deserializer;
        private readonly MessageReplayService _messageReplayService;
        private readonly MessageChannel<AppointmentMessage> _messageChannel;
        private readonly UserBatchChannel _userBatchChannel;
        
        // Kafka consumer
        private IConsumer<string, byte[]> _consumer;
        
        // Processing state
        private readonly ConcurrentDictionary<string, AppointmentMessage> _processingBatch = new();
        private readonly ConcurrentDictionary<string, DateTimeOffset> _messageLastSeen = new();
        private Timer _batchTimer;
        private int _inProgressBatchSize = 0;
        private readonly SemaphoreSlim _batchProcessingSemaphore = new(1, 1);
        
        // Constants
        private const int MaxBatchSize = 100;
        private const int BatchTimeoutSeconds = 5;
        private const int MinBatchSize = 10;
        private const int HealthCheckIntervalSeconds = 30;        public AppointmentsHostedService(
            ILogger<AppointmentsHostedService> logger,
            IOptions<KafkaOptions> kafkaOptions,
            IOptions<ApplicationOptions> appOptions,
            BatchProcessor batchProcessor,
            AvroMessageDeserializer deserializer,
            MessageReplayService messageReplayService,
            MessageChannel<AppointmentMessage> messageChannel,
            UserBatchChannel userBatchChannel)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _kafkaOptions = kafkaOptions?.Value ?? throw new ArgumentNullException(nameof(kafkaOptions));
            _appOptions = appOptions?.Value ?? throw new ArgumentNullException(nameof(appOptions));
            _batchProcessor = batchProcessor ?? throw new ArgumentNullException(nameof(batchProcessor));
            _deserializer = deserializer ?? throw new ArgumentNullException(nameof(deserializer));
            _messageReplayService = messageReplayService ?? throw new ArgumentNullException(nameof(messageReplayService));
            _messageChannel = messageChannel ?? throw new ArgumentNullException(nameof(messageChannel));
            _userBatchChannel = userBatchChannel ?? throw new ArgumentNullException(nameof(userBatchChannel));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation(
                "Starting AppointmentsHostedService, consuming from topic {Topic}", 
                _kafkaOptions.ConsumerTopic);
            
            // Initialize services and connections
            try
            {
                await InitializeAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "Failed to initialize AppointmentsHostedService. Shutting down.");
                return; // Exit the service if initialization fails
            }

            // Create a timer for processing batches
            _batchTimer = new Timer(
                async _ => await TriggerBatchProcessingAsync(stoppingToken), 
                null, 
                TimeSpan.FromSeconds(BatchTimeoutSeconds),
                TimeSpan.FromSeconds(BatchTimeoutSeconds));            // Create task for consuming messages
            var consumptionTask = Task.Run(async () => await ConsumeMessagesAsync(stoppingToken), stoppingToken);
            
            // Create task for channel processing
            var channelProcessingTask = Task.Run(async () => await ProcessMessageChannelAsync(stoppingToken), stoppingToken);
            
            // Create task for user batch channel processing if enabled
            Task userBatchProcessingTask = Task.CompletedTask;
            if (_appOptions.EnableUserBatching)
            {
                userBatchProcessingTask = Task.Run(async () => await _userBatchChannel.StartProcessingAsync(stoppingToken), stoppingToken);
                _logger.LogInformation("User-based batch processing enabled and started");
            }
            
            // Create task for health reporting
            var healthReportingTask = Task.Run(async () => await ReportHealthMetricsAsync(stoppingToken), stoppingToken);
            
            // Create task for dead letter processing
            var deadLetterTask = Task.Run(async () => await ProcessDeadLetterQueueAsync(stoppingToken), stoppingToken);
              // Wait for cancellation
            await Task.WhenAny(
                Task.Delay(Timeout.Infinite, stoppingToken),
                Task.WhenAll(consumptionTask, channelProcessingTask, userBatchProcessingTask, healthReportingTask));
            
            _logger.LogInformation("AppointmentsHostedService stopping...");
            
            // Clean up
            await ShutdownAsync();
            
            _logger.LogInformation("AppointmentsHostedService stopped.");
        }

        /// <summary>
        /// Initializes the service by creating Kafka consumer and other dependencies
        /// </summary>
        private async Task InitializeAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Initializing AppointmentsHostedService");
            
            // Create the Kafka consumer
            var config = new ConsumerConfig
            {
                BootstrapServers = _kafkaOptions.BootstrapServers,
                GroupId = _kafkaOptions.ConsumerGroupId,
                AutoOffsetReset = AutoOffsetReset.Earliest,
                EnableAutoCommit = false, // We'll commit manually after processing
                EnableAutoOffsetStore = false, // Manage offsets manually
                SessionTimeoutMs = 30000, // 30 seconds
                HeartbeatIntervalMs = 10000, // 10 seconds
                MaxPollIntervalMs = 300000, // 5 minutes
                FetchMaxBytes = 52428800, // 50 MB
                MaxPartitionFetchBytes = 1048576, // 1 MB
                StatisticsIntervalMs = 60000, // 1 minute
                // Security settings if needed
                SecurityProtocol = _kafkaOptions.UseSecureConnection ? SecurityProtocol.SaslSsl : SecurityProtocol.Plaintext
            };

            // Add SASL security if configured
            if (_kafkaOptions.UseSecureConnection && !string.IsNullOrEmpty(_kafkaOptions.SaslUsername))
            {
                config.SaslMechanism = SaslMechanism.Plain;
                config.SaslUsername = _kafkaOptions.SaslUsername;
                config.SaslPassword = _kafkaOptions.SaslPassword;
            }
            
            // Create consumer
            _consumer = new ConsumerBuilder<string, byte[]>(config)
                .SetStatisticsHandler((_, stats) => LogConsumerStatistics(stats))
                .SetErrorHandler((_, error) => LogConsumerError(error))
                .Build();
            
            // Subscribe to the topic
            _consumer.Subscribe(_kafkaOptions.ConsumerTopic);
            
            // Initialize message replay service
            await _messageReplayService.InitializeAsync(cancellationToken);
            
            _logger.LogInformation(
                "AppointmentsHostedService initialized and subscribed to topic {Topic}", 
                _kafkaOptions.ConsumerTopic);
        }

        /// <summary>
        /// Main loop for consuming messages from Kafka
        /// </summary>
        private async Task ConsumeMessagesAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting message consumption loop");
            
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var consumeResult = _consumer.Consume(cancellationToken);
                    
                    if (consumeResult != null)
                    {
                        // Process the message
                        await ProcessMessageAsync(consumeResult, cancellationToken);
                    }
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    // Normal shutdown
                    break;
                }
                catch (ConsumeException ex)
                {
                    _logger.LogError(ex, "Error consuming message");
                    // Brief delay to avoid hammering the broker if there's an issue
                    await Task.Delay(1000, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Unexpected error in message consumption loop");
                    // Brief delay to avoid tight loop if there's a persistent error
                    await Task.Delay(1000, cancellationToken);
                }
            }
            
            _logger.LogInformation("Message consumption loop stopped");
        }

        /// <summary>
        /// Processes a single message from Kafka
        /// </summary>
        private async Task ProcessMessageAsync(ConsumeResult<string, byte[]> consumeResult, CancellationToken cancellationToken)
        {
            if (consumeResult?.Message?.Value == null)
            {
                _logger.LogWarning("Received null message, skipping");
                CommitOffset(consumeResult);
                return;
            }
            
            var messageKey = consumeResult.Message.Key;
            var messageValue = consumeResult.Message.Value;
            
            try
            {
                // Deserialize the message
                var appointmentMessage = await _deserializer.DeserializeAsync<AppointmentMessage>(messageValue, cancellationToken);
                
                if (appointmentMessage == null)
                {
                    _logger.LogWarning("Failed to deserialize message, skipping. Key: {Key}", messageKey);
                    CommitOffset(consumeResult);
                    return;
                }
                
                // Set metadata from Kafka
                appointmentMessage.KafkaOffset = consumeResult.Offset.Value;
                appointmentMessage.KafkaPartition = consumeResult.Partition.Value;
                appointmentMessage.KafkaTopic = consumeResult.Topic;
                appointmentMessage.ReceivedAt = DateTimeOffset.UtcNow;
                
                if (string.IsNullOrEmpty(appointmentMessage.CorrelationId))
                {
                    appointmentMessage.CorrelationId = Guid.NewGuid().ToString();
                }                // Route message based on configuration
                if (_appOptions.EnableUserBatching)
                {
                    // Send to user batch channel for optimal user-based batching
                    await _userBatchChannel.WriteAsync(appointmentMessage, cancellationToken);
                }
                else
                {
                    // Send to the message channel for standard processing
                    await _messageChannel.WriteAsync(appointmentMessage, cancellationToken);
                }
                
                // Store offset to be committed after message is processed
                _consumer.StoreOffset(consumeResult);
                
                // Log every 100 messages to avoid flooding logs
                if (consumeResult.Offset % 100 == 0)
                {
                    _logger.LogInformation(
                        "Received message at offset {Offset}, partition {Partition}, key {Key}, correlation {CorrelationId}", 
                        consumeResult.Offset, consumeResult.Partition, messageKey, appointmentMessage.CorrelationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing message at offset {Offset}, partition {Partition}, key {Key}",
                    consumeResult.Offset, consumeResult.Partition, messageKey);
                
                // Move message to dead letter queue
                await _messageReplayService.SendToDeadLetterQueueAsync(
                    messageValue, 
                    ex.Message, 
                    consumeResult.TopicPartitionOffset, 
                    cancellationToken);
                
                // Commit offset for failed message to avoid reprocessing
                CommitOffset(consumeResult);
            }
        }
        
        /// <summary>
        /// Reads messages from the channel and adds them to the processing batch
        /// </summary>
        private async Task ProcessMessageChannelAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting message channel processing loop");
            
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Read from the channel
                    var message = await _messageChannel.ReadAsync(cancellationToken);
                    
                    // Add to the current batch
                    _processingBatch[message.CorrelationId] = message;
                    _messageLastSeen[message.CorrelationId] = DateTimeOffset.UtcNow;
                    
                    // Trigger batch processing if we've reached the max batch size
                    if (_processingBatch.Count >= MaxBatchSize)
                    {
                        await TriggerBatchProcessingAsync(cancellationToken);
                    }
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    // Normal shutdown
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in message channel processing loop");
                    // Brief delay to avoid tight loops in error conditions
                    await Task.Delay(1000, cancellationToken);
                }
            }
            
            _logger.LogInformation("Message channel processing loop stopped");
        }
        
        /// <summary>
        /// Triggers batch processing if conditions are met
        /// </summary>
        private async Task TriggerBatchProcessingAsync(CancellationToken cancellationToken)
        {
            // Skip if already processing a batch or if cancellation requested
            if (_inProgressBatchSize > 0 || cancellationToken.IsCancellationRequested)
            {
                return;
            }
            
            // Skip if batch is too small and it's not a timer-triggered batch
            if (_processingBatch.Count < MinBatchSize)
            {
                return;
            }
            
            // Ensure only one batch processing happens at a time
            if (!await _batchProcessingSemaphore.WaitAsync(0))
            {
                return;
            }

            try
            {
                // Get the current batch
                var batchToProcess = _processingBatch.Values.ToList();
                if (batchToProcess.Count == 0)
                {
                    return;
                }
                
                // Set batch size flag to prevent new batches during processing
                _inProgressBatchSize = batchToProcess.Count;
                
                // Clear the current batch
                foreach (var message in batchToProcess)
                {
                    _processingBatch.TryRemove(message.CorrelationId, out _);
                    _messageLastSeen.TryRemove(message.CorrelationId, out _);
                }
                
                // Process the batch
                _logger.LogInformation("Processing batch of {Count} messages", batchToProcess.Count);
                var stopwatch = Stopwatch.StartNew();
                
                try
                {
                    var result = await _batchProcessor.ProcessAppointmentBatchAsync(batchToProcess, cancellationToken);
                    
                    stopwatch.Stop();
                    _logger.LogInformation(
                        "Batch processing completed in {ElapsedMs}ms: {SuccessCount} successful, {FailedCount} failed, {SkippedCount} skipped",
                        stopwatch.ElapsedMilliseconds, result.SuccessfulMessages, result.FailedMessages, result.SkippedMessages);
                    
                    // Commit offsets after processing
                    CommitOffsets();

                    // Record metrics
                    RecordBatchMetrics(result);
                    
                    // Send failed messages to dead letter queue
                    HandleFailedMessages(
                        batchToProcess, 
                        result.SuccessfulMessages, 
                        result.FailedMessages, 
                        result.SkippedMessages);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing batch of {Count} messages", batchToProcess.Count);
                }
                finally
                {
                    // Reset the batch size flag
                    _inProgressBatchSize = 0;
                }
            }
            finally
            {
                // Release the semaphore
                _batchProcessingSemaphore.Release();
            }
        }
        
        /// <summary>
        /// Handles failed messages by sending them to the dead letter queue
        /// </summary>
        private void HandleFailedMessages(
            List<AppointmentMessage> batch, 
            int successCount, 
            int failedCount, 
            int skippedCount)
        {
            if (failedCount == 0 && skippedCount == 0)
            {
                return;
            }
            
            // For now, we're not doing anything special with failed messages
            // since they're already being tracked in the database with their error status
            // and will be picked up by the message replay service
        }
        
        /// <summary>
        /// Records metrics for a batch processing operation
        /// </summary>
        private void RecordBatchMetrics(BatchProcessingResult result)
        {
            // Here we would integrate with a metrics system like DataDog
            // For now, just log the metrics
            _logger.LogInformation(
                "Metrics: batch_size={BatchSize}, success_count={SuccessCount}, fail_count={FailCount}, " +
                "skip_count={SkipCount}, duration_ms={DurationMs}, users_count={UsersCount}",
                result.TotalMessages,
                result.SuccessfulMessages,
                result.FailedMessages,
                result.SkippedMessages,
                result.ProcessingTime.TotalMilliseconds,
                result.SuccessfulUsers.Count + result.PartiallyFailedUsers.Count + result.FailedUsers.Count);
        }
        
        /// <summary>
        /// Commits the offset for a specific consume result
        /// </summary>
        private void CommitOffset(ConsumeResult<string, byte[]> consumeResult)
        {
            try
            {
                if (consumeResult != null)
                {
                    _consumer.Commit(consumeResult);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error committing offset {Offset} for partition {Partition}", 
                    consumeResult?.Offset, consumeResult?.Partition);
            }
        }
        
        /// <summary>
        /// Commits all stored offsets
        /// </summary>
        private void CommitOffsets()
        {
            try
            {
                _consumer.Commit();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error committing offsets");
            }
        }
        
        /// <summary>
        /// Logs consumer errors
        /// </summary>
        private void LogConsumerError(Error error)
        {
            _logger.LogError("Kafka consumer error: {ErrorCode} - {Reason}", error.Code, error.Reason);
        }
        
        /// <summary>
        /// Logs consumer statistics
        /// </summary>
        private void LogConsumerStatistics(string statistics)
        {
            // Log statistics at debug level to avoid flooding logs
            _logger.LogDebug("Kafka consumer statistics: {Statistics}", statistics);
        }
        
        /// <summary>
        /// Reports health metrics periodically
        /// </summary>
        private async Task ReportHealthMetricsAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting health reporting loop");
            
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {                    // Report current state
                    if (_appOptions.EnableUserBatching)
                    {
                        _logger.LogInformation(
                            "Health: Queue_Size={QueueSize}, Batch_Size={BatchSize}, Processing_Size={ProcessingSize}, UserBatching=Enabled",
                            _messageChannel.Count,
                            _processingBatch.Count,
                            _inProgressBatchSize);
                    }
                    else
                    {
                        _logger.LogInformation(
                            "Health: Queue_Size={QueueSize}, Batch_Size={BatchSize}, Processing_Size={ProcessingSize}, UserBatching=Disabled",
                            _messageChannel.Count,
                            _processingBatch.Count,
                            _inProgressBatchSize);
                    }
                    
                    // Wait for the next interval
                    await Task.Delay(TimeSpan.FromSeconds(HealthCheckIntervalSeconds), cancellationToken);
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    // Normal shutdown
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in health reporting loop");
                    await Task.Delay(1000, cancellationToken);
                }
            }
            
            _logger.LogInformation("Health reporting loop stopped");
        }
        
        /// <summary>
        /// Processes messages in the dead letter queue for replay
        /// </summary>
        private async Task ProcessDeadLetterQueueAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting dead letter queue processing loop");
            
            // Wait a bit before starting to process dead letters
            await Task.Delay(TimeSpan.FromMinutes(1), cancellationToken);
            
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check if it's time to process dead letters (e.g., during off hours)
                    if (ShouldProcessDeadLetters())
                    {
                        await _messageReplayService.ProcessDeadLetterQueueAsync(
                            maxMessages: 50, 
                            maxAgeHours: 24, 
                            cancellationToken);
                    }
                    
                    // Wait for the next interval
                    await Task.Delay(TimeSpan.FromMinutes(15), cancellationToken);
                }
                catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
                {
                    // Normal shutdown
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in dead letter processing loop");
                    await Task.Delay(TimeSpan.FromMinutes(5), cancellationToken);
                }
            }
            
            _logger.LogInformation("Dead letter queue processing loop stopped");
        }
        
        /// <summary>
        /// Determines if we should process dead letters at the current time
        /// </summary>
        private bool ShouldProcessDeadLetters()
        {
            // For now, always process
            // In production, might want to limit to off hours
            return true;
        }
        
        /// <summary>
        /// Performs graceful shutdown of the service
        /// </summary>
        private async Task ShutdownAsync()
        {
            _logger.LogInformation("Performing graceful shutdown");
            
            try
            {
                // Dispose the batch timer
                _batchTimer?.Dispose();
                  // Process any remaining messages in the batch
                if (_processingBatch.Count > 0)
                {
                    _logger.LogInformation("Processing {Count} remaining messages during shutdown", _processingBatch.Count);
                    
                    try
                    {
                        var batch = _processingBatch.Values.ToList();
                        await _batchProcessor.ProcessAppointmentBatchAsync(batch, CancellationToken.None);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing remaining messages during shutdown");
                    }
                }
                
                // Shutdown user batch channel if enabled
                if (_appOptions.EnableUserBatching)
                {
                    try
                    {
                        _logger.LogInformation("Shutting down user batch channel");
                        await _userBatchChannel.ShutdownAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error shutting down user batch channel");
                    }
                }
                
                // Commit final offsets
                try
                {
                    _consumer?.Commit();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error committing offsets during shutdown");
                }
                
                // Close and dispose the consumer
                try
                {
                    _consumer?.Close();
                    _consumer?.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error closing Kafka consumer during shutdown");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during graceful shutdown");
            }
            
            _logger.LogInformation("Graceful shutdown completed");
        }
        
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("StopAsync called");
            
            await ShutdownAsync();
            await base.StopAsync(cancellationToken);
            
            _logger.LogInformation("StopAsync completed");
        }
    }
}
